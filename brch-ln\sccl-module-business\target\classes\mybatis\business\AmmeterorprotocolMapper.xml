<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper">


    <resultMap type="Ammeterorprotocol" id="AmmeterorprotocolResult">
        <id property="id" column="id"/>
        <result property="category" column="category"/>
        <result property="ammetername" column="ammetername"/>
        <result property="protocolname" column="protocolname"/>
        <result property="country" column="country"/>
        <result property="countryName" column="countryName"/>
        <result property="company" column="company"/>
        <result property="substation" column="substation"/>
        <result property="projectname" column="projectname"/>
        <result property="address" column="address"/>
        <result property="payname" column="payname"/>
        <result property="paytype" column="paytype"/>
        <result property="payperiod" column="payperiod"/>
        <result property="paymanager" column="paymanager"/>
        <result property="ammetermanager" column="ammetermanager"/>
        <result property="ammetertype" column="ammetertype"/>
        <result property="electrovalencenature" column="electrovalencenature"/>
        <result property="electronature" column="electronature"/>
        <result property="electrotype" column="electrotype"/>
        <result property="magnification" column="magnification"/>
        <result property="isStdStation" column="is_std_station"/>
        <result property="directsupplyflag" column="directsupplyflag"/>
        <result property="price" column="price"/>
        <result property="packagetype" column="packagetype"/>
        <result property="outeruser" column="outeruser"/>
        <result property="userunit" column="userunit"/>
        <result property="location" column="location"/>
        <result property="contractname" column="contractname"/>
        <result property="officephone" column="officephone"/>
        <result property="telephone" column="telephone"/>
        <result property="receiptaccountname" column="receiptaccountname"/>
        <result property="receiptaccountbank" column="receiptaccountbank"/>
        <result property="receiptaccounts" column="receiptaccounts"/>
        <result property="protocolscan" column="protocolscan"/>
        <result property="protocolsigneddate" column="protocolsigneddate"/>
        <result property="protocolterminatedate" column="protocolterminatedate"/>
        <result property="status" column="status"/>
        <result property="fee" column="fee"/>
        <result property="wastageFlag" column="wastage_flag"/>
        <result property="maxdegree" column="maxdegree"/>
        <result property="inputdate" column="inputdate"/>
        <result property="inputuser" column="inputuser"/>
        <result property="memo" column="memo"/>
        <result property="substationid" column="substationid"/>
        <result property="mapname" column="mapname"/>
        <result property="sourcetype" column="sourcetype"/>
        <result property="sourceinvoicetype" column="sourceinvoicetype"/>
        <result property="supplytype" column="supplytype"/>
        <result property="supplyinvoicetype" column="supplyinvoicetype"/>
        <result property="property" column="property"/>
        <result property="paymanagerTele" column="paymanager_tele"/>
        <result property="percent" column="percent"/>
        <result property="iprocessinstid" column="iprocessinstid"/>
        <result property="finishTime" column="finish_time"/>
        <result property="finishFlag" column="finish_flag"/>
        <result property="phyMac" column="phy_mac"/>
        <result property="phyName" column="phy_name"/>
        <result property="jtStationid" column="jt_stationid"/>
        <result property="jtDeviceid" column="jt_deviceid"/>
        <result property="jtMonth" column="jt_month"/>
        <result property="jtType" column="jt_type"/>
        <result property="jtHousecode" column="jt_housecode"/>
        <result property="jtStationname" column="jt_stationname"/>
        <result property="jtDevicename" column="jt_devicename"/>
        <result property="jtHousename" column="jt_housename"/>
        <result property="jtPhyMac" column="jt_phy_mac"/>
        <result property="contractOthPart" column="contract_oth_part"/>
        <result property="stationName" column="station_name"/>
        <result property="nmCcode" column="nm_ccode"/>
        <result property="nmL2100" column="nm_l2100"/>
        <result property="nmL1800" column="nm_l1800"/>
        <result property="nmCl800m" column="nm_cl800m"/>
        <result property="islumpsum" column="islumpsum"/>
        <result property="lumpstartdate" column="lumpstartdate"/>
        <result property="lumprrunum" column="lumprrunum"/>
        <result property="supplybureauammetercode" column="supplybureauammetercode"/>
        <result property="issmartammeter" column="issmartammeter"/>
        <result property="supplybureauname" column="supplybureauname"/>
        <result property="generationof" column="generationof"/>
        <result property="quotapowerratio" column="quotapowerratio"/>
        <result property="stationcode" column="stationcode"/>
        <result property="stationstatus" column="stationstatus"/>
        <result property="stationtype" column="stationtype"/>
        <result property="stationaddress" column="stationaddress"/>
        <result property="stationaddresscode" column="stationaddresscode"/>
        <result property="isairconditioning" column="isairconditioning"/>
        <result property="countryName" column="countryName"/>
        <result property="companyName" column="companyName"/>
        <result property="vouchelectricity" column="vouchelectricity"/>
        <result property="billStatus" column="bill_status"/>
        <result property="isAttach" column="is_attach"/>
        <result property="electrotypename" column="electrotypename"/>
        <result property="processinstId" column="processinstId"/>
        <result property="ammeteruse" column="ammeteruse"/>
        <result property="ammeterno" column="ammeterno"/>
        <result property="isentityammeter" column="isentityammeter"/>
        <result property="delFlag" column="del_flag"/>
        <result property="busiAlias" column="busiAlias"/>
        <result property="createTime" column="create_time"/>
        <result property="quotaId" column="quotaId"/>
        <result property="ammetertypename" column="ammetertypename"/>
        <result property="parentCode" column="parentCode"/>
        <result property="parentId" column="parent_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customerName"/>
        <result property="creatorId" column="creator_id"/>
        <result property="updateById" column="update_id"/>
        <result property="creatorName" column="creatorName"/>
        <result property="updateByName" column="updateName"/>
        <result property="oldAmmeterId" column="old_ammeter_id"/>
        <result property="oldAmmeterName" column="oldAmmeterName1"/>
        <result property="ischangeammeter" column="ischangeammeter"/>
        <result property="oldBillPower" column="old_bill_power"/>
        <result property="ybgPower" column="ybg_power"/>
        <result property="lumpenddate" column="lumpenddate"/>
        <result property="iszgz" column="iszgz"/>
        <result property="oldammetername" column="oldammetername"/>
        <result property="voltageClass" column="voltageClass"/>
        <result property="directFlag" column="iszg"/>
        <result property="officeFlag" column="isbg"/>
        <result property="transdistricompany" column="transdistricompany"/>
        <result property="termname" column="termname"/>
    </resultMap>

    <sql id="selectVo">
        select pap.id,
               pap.category,
               pap.ammetername,
               pap.protocolname,
               pap.country,
               pap.company,
               pap.substation,
               pap.projectname,
               pap.address,
               pap.payname,
               pap.paytype,
               pap.payperiod,
               pap.paymanager,
               pap.ammetermanager,
               pap.ammetertype,
               pap.electrovalencenature,
               pap.electronature,
               pap.electrotype,
               pap.magnification,
               pap.is_std_station,
               pap.directsupplyflag,
               pap.price,
               pap.packagetype,
               pap.outeruser,
               pap.userunit,
               pap.location,
               pap.contractname,
               pap.officephone,
               pap.telephone,
               pap.receiptaccountname,
               pap.receiptaccountbank,
               pap.receiptaccounts,
               pap.protocolscan,
               pap.protocolsigneddate,
               pap.protocolterminatedate,
               pap.status,
               pap.fee,
               pap.wastage_flag,
               pap.maxdegree,
               pap.inputdate,
               pap.inputuser,
               pap.memo,
               pap.substationid,
               pap.mapname,
               pap.sourcetype,
               pap.sourceinvoicetype,
               pap.supplytype,
               pap.supplyinvoicetype,
               pap.property,
               pap.paymanager_tele,
               pap.percent,
               pap.iprocessinstid,
               pap.finish_time,
               pap.finish_flag,
               pap.phy_mac,
               pap.phy_name,
               pap.jt_stationid,
               pap.jt_deviceid,
               pap.jt_month,
               pap.jt_type,
               pap.jt_housecode,
               pap.jt_stationname,
               pap.jt_devicename,
               pap.jt_housename,
               pap.jt_phy_mac,
               pap.contract_oth_part,
               pap.station_name,
               pap.nm_ccode,
               pap.nm_l2100,
               pap.nm_l1800,
               pap.nm_cl800m,
               pap.islumpsum,
               pap.lumpstartdate,
               pap.lumprrunum,
               pap.supplybureauammetercode,
               pap.issmartammeter,
               pap.supplybureauname,
               pap.generationof,
               pap.quotapowerratio,
               pap.stationcode,
               pap.stationstatus,
               pap.stationtype,
               pap.stationaddress,
               pap.stationaddresscode,
               pap.isairconditioning,
               pap.vouchelectricity,
               pap.bill_status,
               pap.ammeteruse,
               pap.ammeterno,
               pap.isentityammeter,
               pap.del_flag,
               pap.create_time,
               pap.update_time,
               pap.parent_id,
               pap.customer_id,
               pap.creator_id,
               pap.update_id,
               pap.old_ammeter_id,
               pap.ischangeammeter,
               pap.old_bill_power,
               pap.ybg_power,
               pap.lumpenddate,
               pap.voltageClass,
               pap.iszg,
               pap.isbg,
               pap.transdistricompany
            <if test="deployTo == 'sc'">,
                   psi.stationcodeintid as stationcode5gr,
                   psiv.stationname as stationname5gr
            </if>
        from power_ammeterorprotocol pap
        <if test="deployTo == 'sc'">
             LEFT JOIN power_station_info psi on pap.stationcode=psi.id
             LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
    </sql>
    <sql id="selectVo1">
        select pap.id,
               pap.category,
               pap.ammetername,
               pap.protocolname,
               pap.country,
               pap.company,
               pap.substation,
               pap.projectname,
               pap.address,
               pap.payname,
               pap.paytype,
               pap.payperiod,
               pap.paymanager,
               pap.ammetermanager,
               pap.ammetertype,
               pap.electrovalencenature,
               pap.electronature,
               pap.electrotype,
               pap.magnification,
               pap.is_std_station,
               pap.directsupplyflag,
               pap.price,
               pap.packagetype,
               pap.outeruser,
               pap.userunit,
               pap.location,
               pap.contractname,
               pap.officephone,
               pap.telephone,
               pap.receiptaccountname,
               pap.receiptaccountbank,
               pap.receiptaccounts,
               pap.protocolscan,
               pap.protocolsigneddate,
               pap.protocolterminatedate,
               pap.status,
               pap.fee,
               pap.wastage_flag,
               pap.maxdegree,
               pap.inputdate,
               pap.inputuser,
               pap.memo,
               pap.substationid,
               pap.mapname,
               pap.sourcetype,
               pap.sourceinvoicetype,
               pap.supplytype,
               pap.supplyinvoicetype,
               pap.property,
               pap.paymanager_tele,
               pap.percent,
               pap.iprocessinstid,
               pap.finish_time,
               pap.finish_flag,
               pap.phy_mac,
               pap.phy_name,
               pap.jt_stationid,
               pap.jt_deviceid,
               pap.jt_month,
               pap.jt_type,
               pap.jt_housecode,
               pap.jt_stationname,
               pap.jt_devicename,
               pap.jt_housename,
               pap.jt_phy_mac,
               pap.contract_oth_part,
               pap.station_name,
               pap.nm_ccode,
               pap.nm_l2100,
               pap.nm_l1800,
               pap.nm_cl800m,
               pap.islumpsum,
               pap.lumpstartdate,
               pap.lumprrunum,
               pap.supplybureauammetercode,
               pap.issmartammeter,
               pap.supplybureauname,
               pap.generationof,
               pap.quotapowerratio,
               pap.stationcode,
               pap.stationstatus,
               pap.stationtype,
               pap.stationaddress,
               pap.stationaddresscode,
               pap.isairconditioning,
               pap.del_flag,
               pap.vouchelectricity,
               pap.bill_status,
               pap.ammeteruse,
               pap.ammeterno,
               pap.isentityammeter,
               pap.parent_id,
               pap.creator_id,
               pap.update_id,
               pap.old_ammeter_id,
               pap.ischangeammeter,
               pap.old_bill_power,
               pap.ybg_power,
               pap.lumpenddate,
               pap.iszgz,
               pap.oldammetername,
               pap.voltageClass,
               pap.iszg,
               pap.isbg,
            <if test="deployTo == 'sc'">
                   psi.stationcodeintid as stationcode5gr,
                   psiv.stationname as stationname5gr,
            </if>
               pap.transdistricompany,
               (select (case category when 1 then ammetername else protocolname end)
                from power_ammeterorprotocol
                where id = pap.parent_id limit 1) parentCode,
            (select (case category when 1 then ammetername else protocolname end) from power_ammeterorprotocol where id=pap.old_ammeter_id limit 1) oldAmmeterName1,
            pap.customer_id,
            (select name1 from mss_abccustomer where KUNNR=pap.customer_id and INF_STATUS =1 limit 1) customerName,
            pec.type_name electrotypename,
            power_station_info.resstationcode
        from power_ammeterorprotocol pap
        left join power_station_info on power_station_info.id = pap.stationcode
        LEFT JOIN power_electric_classification pec on pec.id=pap.electrotype
            <if test="deployTo == 'sc'">
                    LEFT JOIN power_station_info psi on pap.stationcode=psi.id
                LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
            </if>
    </sql>
    <sql id="selectAll">
        select
        pap.id,
        pap.category,
        pap.ammetername,
        pap.protocolname,
        pap.country,
        pap.company,
        pap.substation,
        pap.projectname,
        pap.address,
        pap.payname,
        pap.paytype,
        pap.payperiod,
        pap.paymanager,
        pap.ammetermanager,
        pap.ammetertype,
        pap.electrovalencenature,
        pap.electronature,
        pap.electrotype,
        pap.magnification,
        pap.is_std_station,
        pap.directsupplyflag,
        pap.price,
        pap.packagetype,
        pap.outeruser,
        pap.userunit,
        pap.location,
        pap.contractname,
        pap.officephone,
        pap.telephone,
        pap.receiptaccountname,
        pap.receiptaccountbank,
        pap.receiptaccounts,
        pap.protocolscan,
        pap.protocolsigneddate,
        pap.protocolterminatedate,
        pap.status,
        pap.fee,
        pap.wastage_flag,
        pap.maxdegree,
        pap.inputdate,
        pap.inputuser,
        pap.memo,
        pap.substationid,
        pap.mapname,
        pap.sourcetype,
        pap.sourceinvoicetype,
        pap.supplytype,
        pap.supplyinvoicetype,
        pap.property,
        pap.paymanager_tele,
        pap.percent,
        pap.iprocessinstid,
        pap.finish_time,
        pap.finish_flag,
        pap.phy_mac,
        pap.phy_name,
        pap.jt_stationid,
        pap.jt_deviceid,
        pap.jt_month,
        pap.jt_type,
        pap.jt_housecode,
        pap.jt_stationname,
        pap.jt_devicename,
        pap.jt_housename,
        pap.jt_phy_mac,
        pap.contract_oth_part,
        pap.station_name,
        pap.nm_ccode,
        pap.nm_l2100,
        pap.nm_l1800,
        pap.nm_cl800m,
        pap.islumpsum,
        pap.lumpstartdate,
        pap.lumprrunum,
        pap.supplybureauammetercode,
        pap.issmartammeter,
        pap.supplybureauname,
        pap.generationof,
        pap.quotapowerratio,
        pap.stationcode,
        pap.stationstatus,
        pap.stationtype,
        pap.stationaddress,
        pap.stationaddresscode,
        pap.isairconditioning,
        pap.del_flag,
        pap.vouchelectricity,
        pap.bill_status,
        pap.create_time,
        pap.parent_id,
        pap.customer_id,
        '0' is_attach,
        concat((case when country1.org_name is not null then concat(country1.org_name,'-') else ''
        end),country.org_name) countryName,
        company.org_name companyName,
        pap.ammeteruse,
        pap.ammeterno,
        pap.isentityammeter,
        concat((case when pec1.type_name is not null then concat(pec1.type_name,'/') else '' end),pec.type_name)
        electrotypename,
        proc.busi_alias busiAlias,
        proc.id processinstId,
        pap.creator_id,
        pap.update_id,
        pap.old_ammeter_id,
        pap.ischangeammeter,
        pap.old_bill_power,
        pap.ybg_power,
        pap.lumpenddate,
        pap.iszgz,
        <if test="deployTo == 'sc'">
            psi.stationcodeintid as stationcode5gr,
            psiv.stationname as stationname5gr,
        </if>
        pap.oldammetername,
        pap.voltageClass,
        pap.iszg,
        pap.isbg,
        pap.transdistricompany,
        pq.id quotaId
        from
        (select pap.id from power_ammeterorprotocol pap where pap.del_flag = '0'
        <include refid="like1-condition"/>
        ) p
        inner join power_ammeterorprotocol pap on p.id = pap.id

        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) company on company.id = pap.company
        LEFT JOIN (select org.id,org.org_name,PARENT_COMPANY_NO parent_id from rmp.sys_organizations org) country on
        country.id = pap.country
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) country1 on country1.id =
        country.parent_id and country1.id !=pap.company
        LEFT JOIN (select a.id,a.type_name,parent_id from power_electric_classification a) pec on pec.id=pap.electrotype
        LEFT JOIN (select a.id,a.type_name from power_electric_classification a) pec1 on pec1.id=pec.parent_id
        left join (SELECT pi.id,pi.busi_key,pi.busi_alias FROM wf_proc_inst pi
        RIGHT JOIN (SELECT MAX(id) id,MAX(start_time) start_time FROM wf_proc_inst
        where busi_alias in ('MODIFY_PTC','ADD_PTC','ADD_AMM','MODIFY_AMM','AMM_SWITCH_AMM','PRO_SWITCH_AMM') GROUP BY
        busi_key) tmp
        ON pi.start_time = tmp.start_time and tmp.id=pi.id) proc ON proc.busi_key = pap.id
        left join (SELECT max(id) id, device_id FROM power_quota WHERE del_flag = '0' group by device_id) pq on pap.id =pq.device_id
        <if test="deployTo == 'sc'">
            LEFT JOIN power_station_info psi on pap.stationcode=psi.id
            LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
        ORDER BY pap.update_time is null,pap.update_time desc,pap.create_time desc
    </sql>
    <sql id="selectAllLN">
        select
        pap.id,
        pap.category,
        pap.ammetername,
        pap.protocolname,
        pap.country,
        pap.company,
        pap.substation,
        pap.projectname,
        pap.address,
        pap.payname,
        pap.paytype,
        pap.payperiod,
        pap.paymanager,
        pap.ammetermanager,
        pap.ammetertype,
        pap.electrovalencenature,
        pap.electronature,
        pap.electrotype,
        pap.magnification,
        pap.is_std_station,
        pap.directsupplyflag,
        pap.price,
        pap.packagetype,
        pap.outeruser,
        pap.userunit,
        pap.location,
        pap.contractname,
        pap.officephone,
        pap.telephone,
        pap.receiptaccountname,
        pap.receiptaccountbank,
        pap.receiptaccounts,
        pap.protocolscan,
        pap.protocolsigneddate,
        pap.protocolterminatedate,
        pap.status,
        pap.fee,
        pap.wastage_flag,
        pap.maxdegree,
        pap.inputdate,
        pap.inputuser,
        pap.memo,
        pap.substationid,
        pap.mapname,
        pap.sourcetype,
        pap.sourceinvoicetype,
        pap.supplytype,
        pap.supplyinvoicetype,
        pap.property,
        pap.paymanager_tele,
        pap.percent,
        pap.iprocessinstid,
        pap.finish_time,
        pap.finish_flag,
        pap.phy_mac,
        pap.phy_name,
        pap.jt_stationid,
        pap.jt_deviceid,
        pap.jt_month,
        pap.jt_type,
        pap.jt_housecode,
        pap.jt_stationname,
        pap.jt_devicename,
        pap.jt_housename,
        pap.jt_phy_mac,
        pap.contract_oth_part,
        pap.station_name,
        pap.nm_ccode,
        pap.nm_l2100,
        pap.nm_l1800,
        pap.nm_cl800m,
        pap.islumpsum,
        pap.lumpstartdate,
        pap.lumprrunum,
        pap.supplybureauammetercode,
        pap.issmartammeter,
        pap.supplybureauname,
        pap.generationof,
        pap.quotapowerratio,
        pap.stationcode,
        pap.stationstatus,
        pap.stationtype,
        pap.stationaddress,
        pap.stationaddresscode,
        pap.isairconditioning,
        pap.del_flag,
        pap.vouchelectricity,
        pap.bill_status,
        pap.create_time,
        pap.parent_id,
        pap.customer_id,
        (case when att.id is not null then 1 else 0 end) is_attach,
        concat((case when country1.org_name is not null then concat(country1.org_name,'-') else ''
        end),country.org_name) countryName,
        company.org_name companyName,
        pap.ammeteruse,
        pap.ammeterno,
        pap.isentityammeter,
        concat((case when pec1.type_name is not null then concat(pec1.type_name,'/') else '' end),pec.type_name)
        electrotypename,
        proc.busi_alias busiAlias,
        proc.id processinstId,
        pap.creator_id,
        pap.update_id,
        pap.old_ammeter_id,
        pap.ischangeammeter,
        pap.old_bill_power,
        pap.ybg_power,
        pap.lumpenddate,
        pap.iszgz,
        pap.oldammetername,
        pq.id quotaId,
        power_station_info.resstationcode
        from
        (select pap.id from power_ammeterorprotocol pap
        <if test="stationcode != null">
            inner join (select psi.id from power_station_info psi where psi.stationcode like
            concat('%',#{stationcode},'%')) psi on pap.stationcode=psi.id
        </if>
        where pap.del_flag = '0'<include refid="like1-conditionLN"/>) p
        inner join power_ammeterorprotocol pap on p.id = pap.id
        left join power_station_info on power_station_info.id = pap.stationcode
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) company on company.id = pap.company
        LEFT JOIN (select org.id,org.org_name,PARENT_COMPANY_NO parent_id from rmp.sys_organizations org) country on
        country.id = pap.country
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) country1 on country1.id =
        country.parent_id and country1.id !=pap.company
        LEFT JOIN (select a.id,a.type_name,parent_id from power_electric_classification a) pec on pec.id=pap.electrotype
        LEFT JOIN (select a.id,a.type_name from power_electric_classification a) pec1 on pec1.id=pec.parent_id
        LEFT JOIN (select atta.id,atta.busi_id from attachments atta GROUP BY atta.busi_id) att on att.busi_id= pap.id
        left join (SELECT pi.id,pi.busi_key,pi.busi_alias FROM wf_proc_inst pi
        RIGHT JOIN (SELECT MAX(id) id,MAX(start_time) start_time FROM wf_proc_inst
        where busi_alias in ('MODIFY_PTC','ADD_PTC','ADD_AMM','MODIFY_AMM','AMM_SWITCH_AMM','PRO_SWITCH_AMM') GROUP BY
        busi_key ) tmp
        ON pi.start_time = tmp.start_time and pi.id=tmp.id) proc ON proc.busi_key = pap.id
        left join (SELECT max(id) id, device_id FROM power_quota WHERE del_flag = '0' group by device_id) pq on pap.id =pq.device_id
        <where>
           1=1
            <if test="resstationcode != null">and power_station_info.resstationcode = #{resstationcode}</if>
        </where>
        ORDER BY pap.update_time is null,pap.update_time desc,pap.create_time desc
    </sql>
    <sql id="other-condition">
        <if test="id != null">and pap.id = #{id}</if>
        <if test="category != null">and pap.category = #{category}</if>
        <if test="ammetername != null">and pap.ammetername = #{ammetername}</if>
        <if test="protocolname != null">and pap.protocolname = #{protocolname}</if>
        <if test="country != null">and pap.country = #{country}</if>
        <if test="company != null">and pap.company = #{company}</if>
        <if test="substation != null">and pap.substation = #{substation}</if>
        <if test="projectname != null">and pap.projectname = #{projectname}</if>
        <if test="address != null">and pap.address = #{address}</if>
        <if test="payname != null">and pap.payname = #{payname}</if>
        <if test="paytype != null">and pap.paytype = #{paytype}</if>
        <if test="payperiod != null">and pap.payperiod = #{payperiod}</if>
        <if test="paymanager != null">and pap.paymanager = #{paymanager}</if>
        <if test="ammetermanager != null">and pap.ammetermanager = #{ammetermanager}</if>
        <if test="ammetertype != null">and pap.ammetertype = #{ammetertype}</if>
        <if test="electrovalencenature != null">and pap.electrovalencenature = #{electrovalencenature}</if>
        <if test="electronature != null">and pap.electronature = #{electronature}</if>
        <if test="electrotype != null">and pap.electrotype = #{electrotype}</if>
        <if test="magnification != null">and pap.magnification = #{magnification}</if>
        <if test="isStdStation != null">and pap.is_std_station = #{isStdStation}</if>
        <if test="directsupplyflag != null">and pap.directsupplyflag = #{directsupplyflag}</if>
        <if test="price != null">and pap.price = #{price}</if>
        <if test="packagetype != null">and pap.packagetype = #{packagetype}</if>
        <if test="outeruser != null">and pap.outeruser = #{outeruser}</if>
        <if test="userunit != null">and pap.userunit = #{userunit}</if>
        <if test="location != null">and pap.location = #{location}</if>
        <if test="contractname != null">and pap.contractname = #{contractname}</if>
        <if test="officephone != null">and pap.officephone = #{officephone}</if>
        <if test="telephone != null">and pap.telephone = #{telephone}</if>
        <if test="receiptaccountname != null">and pap.receiptaccountname = #{receiptaccountname}</if>
        <if test="receiptaccountbank != null">and pap.receiptaccountbank = #{receiptaccountbank}</if>
        <if test="receiptaccounts != null">and pap.receiptaccounts = #{receiptaccounts}</if>
        <if test="protocolscan != null">and pap.protocolscan = #{protocolscan}</if>
        <if test="protocolsigneddate != null">and pap.protocolsigneddate = #{protocolsigneddate}</if>
        <if test="protocolterminatedate != null">and pap.protocolterminatedate = #{protocolterminatedate}</if>
        <if test="status != null">and pap.status = #{status}</if>
        <if test="fee != null">and pap.fee = #{fee}</if>
        <if test="wastageFlag != null">and pap.wastage_flag = #{wastageFlag}</if>
        <if test="maxdegree != null">and pap.maxdegree = #{maxdegree}</if>
        <if test="inputdate != null">and pap.inputdate = #{inputdate}</if>
        <if test="inputuser != null">and pap.inputuser = #{inputuser}</if>
        <if test="memo != null">and pap.memo = #{memo}</if>
        <if test="substationid != null">and pap.substationid = #{substationid}</if>
        <if test="mapname != null">and pap.mapname = #{mapname}</if>
        <if test="sourcetype != null">and pap.sourcetype = #{sourcetype}</if>
        <if test="sourceinvoicetype != null">and pap.sourceinvoicetype = #{sourceinvoicetype}</if>
        <if test="supplytype != null">and pap.supplytype = #{supplytype}</if>
        <if test="supplyinvoicetype != null">and pap.supplyinvoicetype = #{supplyinvoicetype}</if>
        <if test="property != null">and pap.property = #{property}</if>
        <if test="paymanagerTele != null">and pap.paymanager_tele = #{paymanagerTele}</if>
        <if test="percent != null">and pap.percent = #{percent}</if>
        <if test="iprocessinstid != null">and pap.iprocessinstid = #{iprocessinstid}</if>
        <if test="finishTime != null">and pap.finish_time = #{finishTime}</if>
        <if test="finishFlag != null">and pap.finish_flag = #{finishFlag}</if>
        <if test="phyMac != null">and pap.phy_mac = #{phyMac}</if>
        <if test="phyName != null">and pap.phy_name = #{phyName}</if>
        <if test="jtStationid != null">and pap.jt_stationid = #{jtStationid}</if>
        <if test="jtDeviceid != null">and pap.jt_deviceid = #{jtDeviceid}</if>
        <if test="jtMonth != null">and pap.jt_month = #{jtMonth}</if>
        <if test="jtType != null">and pap.jt_type = #{jtType}</if>
        <if test="jtHousecode != null">and pap.jt_housecode = #{jtHousecode}</if>
        <if test="jtStationname != null">and pap.jt_stationname = #{jtStationname}</if>
        <if test="jtDevicename != null">and pap.jt_devicename = #{jtDevicename}</if>
        <if test="jtHousename != null">and pap.jt_housename = #{jtHousename}</if>
        <if test="jtPhyMac != null">and pap.jt_phy_mac = #{jtPhyMac}</if>
        <if test="contractOthPart != null">and pap.contract_oth_part = #{contractOthPart}</if>
        <if test="stationName != null">and pap.station_name = #{stationName}</if>
        <if test="nmCcode != null">and pap.nm_ccode = #{nmCcode}</if>
        <if test="nmL2100 != null">and pap.nm_l2100 = #{nmL2100}</if>
        <if test="nmL1800 != null">and pap.nm_l1800 = #{nmL1800}</if>
        <if test="nmCl800m != null">and pap.nm_cl800m = #{nmCl800m}</if>
        <if test="islumpsum != null">and pap.islumpsum = #{islumpsum}</if>
        <if test="lumpstartdate != null">and pap.lumpstartdate = #{lumpstartdate}</if>
        <if test="lumprrunum != null">and pap.lumprrunum = #{lumprrunum}</if>
        <if test="supplybureauammetercode != null">and pap.supplybureauammetercode = #{supplybureauammetercode}</if>
        <if test="issmartammeter != null">and pap.issmartammeter = #{issmartammeter}</if>
        <if test="supplybureauname != null">and pap.supplybureauname = #{supplybureauname}</if>
        <if test="generationof != null">and pap.generationof = #{generationof}</if>
        <if test="quotapowerratio != null">and pap.quotapowerratio = #{quotapowerratio}</if>
        <if test="stationcode != null">and pap.stationcode = #{stationcode}</if>
        <if test="stationstatus != null">and pap.stationstatus = #{stationstatus}</if>
        <if test="stationtype != null">and pap.stationtype = #{stationtype}</if>
        <if test="stationaddress != null">and pap.stationaddress = #{stationaddress}</if>
        <if test="stationaddresscode != null">and pap.stationaddresscode = #{stationaddresscode}</if>
        <if test="isairconditioning != null">and pap.isairconditioning = #{isairconditioning}</if>
        <if test="vouchelectricity != null">and pap.vouchelectricity = #{vouchelectricity}</if>
        <if test="billStatus != null">and pap.bill_status = #{billStatus}</if>
        <if test="isAttach != null">and pap.is_attach = #{isAttach}</if>
        <if test="ammeteruse != null">and pap.ammeteruse = #{ammeteruse}</if>
        <if test="ammeterno != null">and pap.ammeterno = #{ammeterno}</if>
        <if test="isentityammeter != null">and pap.isentityammeter = #{isentityammeter}</if>
        <if test="creatorId != null">and pap.creator_id = #{creatorId}</if>
        <if test="updateById != null">and pap.update_id = #{updateById}</if>
        <if test="delFlag != null">and pap.del_flag = #{delFlag}</if>
        <if test="oldAmmeterId != null">and pap.old_ammeter_id = #{oldAmmeterId}</if>
        <if test="ischangeammeter != null">and pap.ischangeammeter = #{ischangeammeter}</if>
        <if test="oldBillPower != null">and pap.old_bill_power = #{oldBillPower}</if>
        <if test="ybgPower != null">and pap.ybg_power = #{ybgPower}</if>
        <if test="lumpenddate != null">and pap.lumpenddate = #{lumpenddate}</if>
    </sql>

    <sql id="like1-conditionLN">
        <if test="id != null">and pap.id like concat('%', #{id}, '%')</if>
        <!--<if test="category != null">and pap.category like concat('%', #{category}, '%')</if>-->
        <if test="type != 3 and ammetername != null">and pap.ammetername like concat('%', #{ammetername}, '%')</if>
        <if test="type != 3 and protocolname != null">and pap.protocolname like concat('%', #{protocolname}, '%')</if>
        <if test="type == 3 and ammetername != null">and(pap.ammetername like concat('%', #{ammetername}, '%') or
            pap.protocolname like concat('%', #{ammetername}, '%'))
        </if>
        <if test="country != null">and pap.country = #{country}</if>
        <if test="countrys.size != 0">
            and pap.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item.id}
                </foreach>
            </trim>
        </if>
        <if test="company != null">and pap.company = #{company}</if>
        <if test="substation != null">and pap.substation like concat('%', #{substation}, '%')</if>
        <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
        <if test="address != null">and pap.address like concat('%', #{address}, '%')</if>
        <if test="payname != null">and pap.payname like concat('%', #{payname}, '%')</if>
        <if test="paytype != null">and pap.paytype = #{paytype}</if>
        <if test="payperiod != null">and pap.payperiod =#{payperiod}</if>
        <if test="paymanager != null">and pap.paymanager like concat('%', #{paymanager}, '%')</if>
        <if test="ammetermanager != null">and pap.ammetermanager like concat('%', #{ammetermanager}, '%')</if>
        <if test="ammetertype != null">and pap.ammetertype = #{ammetertype}</if>
        <if test="electrovalencenature != null">and pap.electrovalencenature = #{electrovalencenature}</if>
        <if test="electronature != null">and pap.electronature = #{electronature}</if>
        <if test="electrotype != null">and pap.electrotype = #{electrotype}</if>
        <if test="magnification != null">and pap.magnification like concat('%', #{magnification}, '%')</if>
        <if test="isStdStation != null">and pap.is_std_station = #{isStdStation}</if>
        <if test="directsupplyflag != null">and pap.directsupplyflag = #{directsupplyflag}</if>
        <if test="price != null">and pap.price like concat('%', #{price}, '%')</if>
        <if test="packagetype != null">and pap.packagetype = #{packagetype}</if>
        <if test="outeruser != null">and pap.outeruser like concat('%', #{outeruser}, '%')</if>
        <if test="userunit != null">and pap.userunit like concat('%', #{userunit}, '%')</if>
        <if test="location != null">and pap.location like concat('%', #{location}, '%')</if>
        <if test="contractname != null">and pap.contractname like concat('%', #{contractname}, '%')</if>
        <if test="officephone != null">and pap.officephone like concat('%', #{officephone}, '%')</if>
        <if test="telephone != null">and pap.telephone like concat('%', #{telephone}, '%')</if>
        <if test="receiptaccountname != null">and pap.receiptaccountname like concat('%', #{receiptaccountname}, '%')
        </if>
        <if test="receiptaccountbank != null">and pap.receiptaccountbank like concat('%', #{receiptaccountbank}, '%')
        </if>
        <if test="receiptaccounts != null">and pap.receiptaccounts like concat('%', #{receiptaccounts}, '%')</if>
        <if test="protocolscan != null">and pap.protocolscan like concat('%', #{protocolscan}, '%')</if>
        <if test="protocolsigneddate != null">and pap.protocolsigneddate like concat('%', #{protocolsigneddate}, '%')
        </if>
        <if test="protocolterminatedate != null">and pap.protocolterminatedate like concat('%',
            #{protocolterminatedate},
            '%')
        </if>
        <if test="status != null">and pap.status = #{status}</if>
        <if test="fee != null">and pap.fee like concat('%', #{fee}, '%')</if>
        <if test="wastageFlag != null">and pap.wastage_flag = #{wastageFlag}</if>
        <if test="maxdegree != null">and pap.maxdegree like concat('%', #{maxdegree}, '%')</if>
        <if test="inputdate != null">and pap.inputdate like concat('%', #{inputdate}, '%')</if>
        <if test="inputuser != null">and pap.inputuser like concat('%', #{inputuser}, '%')</if>
        <if test="memo != null">and pap.memo like concat('%', #{memo}, '%')</if>
        <if test="substationid != null">and pap.substationid like concat('%', #{substationid}, '%')</if>
        <if test="mapname != null">and pap.mapname like concat('%', #{mapname}, '%')</if>
        <if test="sourcetype != null">and pap.sourcetype like concat('%', #{sourcetype}, '%')</if>
        <if test="sourceinvoicetype != null">and pap.sourceinvoicetype like concat('%', #{sourceinvoicetype}, '%')</if>
        <if test="supplytype != null">and pap.supplytype like concat('%', #{supplytype}, '%')</if>
        <if test="supplyinvoicetype != null">and pap.supplyinvoicetype like concat('%', #{supplyinvoicetype}, '%')</if>
        <if test="property != null">and pap.property = #{property}</if>
        <if test="paymanagerTele != null">and pap.paymanager_tele like concat('%', #{paymanagerTele}, '%')</if>
        <if test="percent != null">and pap.percent like concat('%', #{percent}, '%')</if>
        <if test="iprocessinstid != null">and pap.iprocessinstid like concat('%', #{iprocessinstid}, '%')</if>
        <if test="finishTime != null">and pap.finish_time like concat('%', #{finishTime}, '%')</if>
        <if test="finishFlag != null">and pap.finish_flag like concat('%', #{finishFlag}, '%')</if>
        <if test="phyMac != null">and pap.phy_mac like concat('%', #{phyMac}, '%')</if>
        <if test="phyName != null">and pap.phy_name like concat('%', #{phyName}, '%')</if>
        <if test="jtStationid != null">and pap.jt_stationid like concat('%', #{jtStationid}, '%')</if>
        <if test="jtDeviceid != null">and pap.jt_deviceid like concat('%', #{jtDeviceid}, '%')</if>
        <if test="jtMonth != null">and pap.jt_month like concat('%', #{jtMonth}, '%')</if>
        <if test="jtType != null">and pap.jt_type like concat('%', #{jtType}, '%')</if>
        <if test="jtHousecode != null">and pap.jt_housecode like concat('%', #{jtHousecode}, '%')</if>
        <if test="jtStationname != null">and pap.jt_stationname like concat('%', #{jtStationname}, '%')</if>
        <if test="jtDevicename != null">and pap.jt_devicename like concat('%', #{jtDevicename}, '%')</if>
        <if test="jtHousename != null">and pap.jt_housename like concat('%', #{jtHousename}, '%')</if>
        <if test="jtPhyMac != null">and pap.jt_phy_mac like concat('%', #{jtPhyMac}, '%')</if>
        <if test="contractOthPart != null">and pap.contract_oth_part like concat('%', #{contractOthPart}, '%')</if>
        <if test="stationName != null">and pap.station_name like concat('%', #{stationName}, '%')</if>
        <if test="nmCcode != null">and pap.nm_ccode like concat('%', #{nmCcode}, '%')</if>
        <if test="nmL2100 != null">and pap.nm_l2100 like concat('%', #{nmL2100}, '%')</if>
        <if test="nmL1800 != null">and pap.nm_l1800 like concat('%', #{nmL1800}, '%')</if>
        <if test="nmCl800m != null">and pap.nm_cl800m like concat('%', #{nmCl800m}, '%')</if>
        <if test="islumpsum != null">and pap.islumpsum = #{islumpsum}</if>
        <if test="lumpstartdate != null">and pap.lumpstartdate like concat('%', #{lumpstartdate}, '%')</if>
        <if test="lumprrunum != null">and pap.lumprrunum = #{lumprrunum}</if>

        <if test="supplybureauammetercode != null">and pap.supplybureauammetercode like concat('%',
            #{supplybureauammetercode}, '%')
        </if>
        <if test="issmartammeter != null">and pap.issmartammeter = #{issmartammeter}</if>
        <if test="supplybureauname != null">and pap.supplybureauname like concat('%', #{supplybureauname}, '%')</if>
        <if test="generationof != null">and pap.generationof like concat('%', #{generationof}, '%')</if>
        <if test="quotapowerratio != null">and pap.quotapowerratio like concat('%', #{quotapowerratio}, '%')</if>
        <if test="stationstatus != null">and pap.stationstatus = #{stationstatus}</if>
        <if test="stationtype != null">and pap.stationtype = #{stationtype}</if>
        <if test="stationaddress != null">and pap.stationaddress like concat('%', #{stationaddress}, '%')</if>
        <if test="stationaddresscode != null">and pap.stationaddresscode like concat('%', #{stationaddresscode}, '%')
        </if>
        <if test="isairconditioning != null">and pap.isairconditioning = #{isairconditioning}</if>
        <if test="vouchelectricity != null">and pap.vouchelectricity like concat('%', #{vouchelectricity}, '%')</if>
        <if test="type == null || type == 0">and pap.category = 1</if>
        <if test="type == 1">and pap.category between 2 and 5</if>
        <if test="type == 3 and category!=null">and pap.category = #{category}</if>
        <if test="delFlag != null">and pap.del_flag = #{delFlag}</if>
        <if test="billStatus != null">
            <if test="billStatus == 0 or billStatus == 1">
                AND (pap.bill_status = #{billStatus}
                <if test="creatorId != null">
                    and pap.creator_id = #{creatorId}
                </if>
                )
            </if>
            <if test="billStatus != 0 and billStatus != 1">
                and pap.bill_status = #{billStatus}
            </if>
        </if>
        <if test="billStatus == null">AND (pap.bill_status not in(0,1) or (pap.bill_status in(0,1)
            <if test="creatorId != null">
                and pap.creator_id = #{creatorId}
            </if>
            ))
        </if>
        <if test="isAttach != null">and pap.is_attach = #{isAttach}</if>
        <if test="ammeteruse != null">and pap.ammeteruse = #{ammeteruse}</if>
        <if test="ammeterno != null">and pap.ammeterno like concat('%', #{ammeterno}, '%')</if>
        <if test="isentityammeter != null">and isentityammeter = #{isentityammeter}</if>
        <if test="oldAmmeterId != null">and old_ammeter_id = #{oldAmmeterId}</if>
        <if test="ischangeammeter != null">and ischangeammeter = #{ischangeammeter}</if>
        <if test="oldBillPower != null">and old_bill_power like concat('%',#{oldBillPower},'%')</if>
        <if test="ybgPower != null">and ybg_power like concat('%',#{ybgPower},'%')</if>
        <if test="lumpenddate != null">and lumpenddate like concat('%',#{lumpenddate},'%')</if>
        <!--        <if test="creatorId != null"> and pap.creator_id = #{creatorId}</if>-->
        <!--        <if test="updateById != null"> and pap.update_id = #{updateById}</if>-->
        <if test="electrotypes.size != 0">
            and pap.electrotype in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="electrotypes" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="stationcode5gr != null and stationcode5gr != ''">
            and pap.stationcode in (
                SELECT id FROM power_station_info WHERE `status` = '2' AND stationcodeintid LIKE concat('%',#{stationcode5gr},'%')
            )
        </if>
        <if test="stationname5gr != null and stationname5gr != ''">
            and pap.stationcode in (
                SELECT id FROM power_station_info WHERE `status` = '2' AND stationname5gr LIKE concat('%',#{stationname5gr},'%')
            )
        </if>
        <if test="stationname5gr != null and stationname5gr != ''">
        </if>
    </sql>
    <sql id="like1-condition">
        <if test="id != null">and pap.id =#{id}</if>
        <if test="category != null">and pap.category like concat('%', #{category}, '%')</if>
        <if test="ammetername != null">and pap.ammetername like concat('%', #{ammetername}, '%')</if>
        <if test="protocolname != null">and pap.protocolname like concat('%', #{protocolname}, '%')</if>
        <if test="country != null">and pap.country = #{country}</if>
        <if test="countrys.size != 0">
            and pap.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item.id}
                </foreach>
            </trim>
        </if>
        <if test="company != null">and pap.company = #{company}</if>
        <if test="substation != null">and pap.substation like concat('%', #{substation}, '%')</if>
        <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
        <if test="address != null">and pap.address like concat('%', #{address}, '%')</if>
        <if test="payname != null">and pap.payname like concat('%', #{payname}, '%')</if>
        <if test="paytype != null">and pap.paytype = #{paytype}</if>
        <if test="payperiod != null">and pap.payperiod =#{payperiod}</if>
        <if test="paymanager != null">and pap.paymanager like concat('%', #{paymanager}, '%')</if>
        <if test="ammetermanager != null">and pap.ammetermanager like concat('%', #{ammetermanager}, '%')</if>
        <if test="ammetertype != null">and pap.ammetertype = #{ammetertype}</if>
        <if test="electrovalencenature != null">and pap.electrovalencenature = #{electrovalencenature}</if>
        <if test="electronature != null">and pap.electronature = #{electronature}</if>
        <if test="electrotype != null">and pap.electrotype = #{electrotype}</if>
        <if test="magnification != null">and pap.magnification like concat('%', #{magnification}, '%')</if>
        <if test="isStdStation != null">and pap.is_std_station = #{isStdStation}</if>
        <if test="directsupplyflag != null">and pap.directsupplyflag = #{directsupplyflag}</if>
        <if test="price != null">and pap.price like concat('%', #{price}, '%')</if>
        <if test="packagetype != null">and pap.packagetype = #{packagetype}</if>
        <if test="outeruser != null">and pap.outeruser like concat('%', #{outeruser}, '%')</if>
        <if test="userunit != null">and pap.userunit like concat('%', #{userunit}, '%')</if>
        <if test="location != null">and pap.location like concat('%', #{location}, '%')</if>
        <if test="contractname != null">and pap.contractname like concat('%', #{contractname}, '%')</if>
        <if test="officephone != null">and pap.officephone like concat('%', #{officephone}, '%')</if>
        <if test="telephone != null">and pap.telephone like concat('%', #{telephone}, '%')</if>
        <if test="receiptaccountname != null">and pap.receiptaccountname like concat('%', #{receiptaccountname}, '%')
        </if>
        <if test="receiptaccountbank != null">and pap.receiptaccountbank like concat('%', #{receiptaccountbank}, '%')
        </if>
        <if test="receiptaccounts != null">and pap.receiptaccounts like concat('%', #{receiptaccounts}, '%')</if>
        <if test="protocolscan != null">and pap.protocolscan like concat('%', #{protocolscan}, '%')</if>
        <if test="protocolsigneddate != null">and pap.protocolsigneddate like concat('%', #{protocolsigneddate}, '%')
        </if>
        <if test="protocolterminatedate != null">and pap.protocolterminatedate like concat('%',
            #{protocolterminatedate},
            '%')
        </if>
        <if test="status != null">and pap.status = #{status}</if>
        <if test="fee != null">and pap.fee like concat('%', #{fee}, '%')</if>
        <if test="wastageFlag != null">and pap.wastage_flag = #{wastageFlag}</if>
        <if test="maxdegree != null">and pap.maxdegree like concat('%', #{maxdegree}, '%')</if>
        <if test="inputdate != null">and pap.inputdate like concat('%', #{inputdate}, '%')</if>
        <if test="inputuser != null">and pap.inputuser like concat('%', #{inputuser}, '%')</if>
        <if test="memo != null">and pap.memo like concat('%', #{memo}, '%')</if>
        <if test="substationid != null">and pap.substationid like concat('%', #{substationid}, '%')</if>
        <if test="mapname != null">and pap.mapname like concat('%', #{mapname}, '%')</if>
        <if test="sourcetype != null">and pap.sourcetype like concat('%', #{sourcetype}, '%')</if>
        <if test="sourceinvoicetype != null">and pap.sourceinvoicetype like concat('%', #{sourceinvoicetype}, '%')</if>
        <if test="supplytype != null">and pap.supplytype like concat('%', #{supplytype}, '%')</if>
        <if test="supplyinvoicetype != null">and pap.supplyinvoicetype like concat('%', #{supplyinvoicetype}, '%')</if>
        <if test="property != null">and pap.property = #{property}</if>
        <if test="paymanagerTele != null">and pap.paymanager_tele like concat('%', #{paymanagerTele}, '%')</if>
        <if test="percent != null">and pap.percent like concat('%', #{percent}, '%')</if>
        <if test="iprocessinstid != null">and pap.iprocessinstid like concat('%', #{iprocessinstid}, '%')</if>
        <if test="finishTime != null">and pap.finish_time like concat('%', #{finishTime}, '%')</if>
        <if test="finishFlag != null">and pap.finish_flag like concat('%', #{finishFlag}, '%')</if>
        <if test="phyMac != null">and pap.phy_mac like concat('%', #{phyMac}, '%')</if>
        <if test="phyName != null">and pap.phy_name like concat('%', #{phyName}, '%')</if>
        <if test="jtStationid != null">and pap.jt_stationid like concat('%', #{jtStationid}, '%')</if>
        <if test="jtDeviceid != null">and pap.jt_deviceid like concat('%', #{jtDeviceid}, '%')</if>
        <if test="jtMonth != null">and pap.jt_month like concat('%', #{jtMonth}, '%')</if>
        <if test="jtType != null">and pap.jt_type like concat('%', #{jtType}, '%')</if>
        <if test="jtHousecode != null">and pap.jt_housecode like concat('%', #{jtHousecode}, '%')</if>
        <if test="jtStationname != null">and pap.jt_stationname like concat('%', #{jtStationname}, '%')</if>
        <if test="jtDevicename != null">and pap.jt_devicename like concat('%', #{jtDevicename}, '%')</if>
        <if test="jtHousename != null">and pap.jt_housename like concat('%', #{jtHousename}, '%')</if>
        <if test="jtPhyMac != null">and pap.jt_phy_mac like concat('%', #{jtPhyMac}, '%')</if>
        <if test="contractOthPart != null">and pap.contract_oth_part like concat('%', #{contractOthPart}, '%')</if>
        <if test="stationName != null">and pap.station_name like concat('%', #{stationName}, '%')</if>
        <if test="nmCcode != null">and pap.nm_ccode like concat('%', #{nmCcode}, '%')</if>
        <if test="nmL2100 != null">and pap.nm_l2100 like concat('%', #{nmL2100}, '%')</if>
        <if test="nmL1800 != null">and pap.nm_l1800 like concat('%', #{nmL1800}, '%')</if>
        <if test="nmCl800m != null">and pap.nm_cl800m like concat('%', #{nmCl800m}, '%')</if>
        <if test="islumpsum != null">and pap.islumpsum = #{islumpsum}</if>
        <if test="lumpstartdate != null">and pap.lumpstartdate like concat('%', #{lumpstartdate}, '%')</if>
        <if test="lumprrunum != null">and pap.lumprrunum = #{lumprrunum}</if>

        <if test="supplybureauammetercode != null">and pap.supplybureauammetercode like concat('%',
            #{supplybureauammetercode}, '%')
        </if>
        <if test="issmartammeter != null">and pap.issmartammeter = #{issmartammeter}</if>
        <if test="supplybureauname != null">and pap.supplybureauname like concat('%', #{supplybureauname}, '%')</if>
        <if test="generationof != null">and pap.generationof like concat('%', #{generationof}, '%')</if>
        <if test="quotapowerratio != null">and pap.quotapowerratio like concat('%', #{quotapowerratio}, '%')</if>
        <if test="stationcode != null">and pap.stationcode like concat('%', #{stationcode}, '%')</if>
        <if test="stationstatus != null">and pap.stationstatus = #{stationstatus}</if>
        <if test="stationtype != null">and pap.stationtype = #{stationtype}</if>
        <if test="stationaddress != null">and pap.stationaddress like concat('%', #{stationaddress}, '%')</if>
        <if test="stationaddresscode != null">and pap.stationaddresscode like concat('%', #{stationaddresscode}, '%')
        </if>
        <if test="isairconditioning != null">and pap.isairconditioning = #{isairconditioning}</if>
        <if test="vouchelectricity != null">and pap.vouchelectricity like concat('%', #{vouchelectricity}, '%')</if>
        <if test="type == null || type == 0">and pap.category = 1</if>
        <if test="type == 1">and pap.category between 2 and 5</if>
        <if test="delFlag != null">and pap.del_flag = #{delFlag}</if>
        <if test="billStatus != null">
            <if test="billStatus == 0 or billStatus == 1">
                AND (pap.bill_status = #{billStatus}
                <if test="creatorId != null">
                    and pap.creator_id = #{creatorId}
                </if>
                )
            </if>
            <if test="billStatus != 0 and billStatus != 1">
                and pap.bill_status = #{billStatus}
            </if>
        </if>
        <if test="billStatus == null">AND (pap.bill_status not in(0,1) or (pap.bill_status in(0,1)
            <if test="creatorId != null">
                and pap.creator_id = #{creatorId}
            </if>
            ))
        </if>
        <if test="isAttach != null">and pap.is_attach = #{isAttach}</if>
        <if test="ammeteruse != null">and pap.ammeteruse = #{ammeteruse}</if>
        <if test="ammeterno != null">and pap.ammeterno like concat('%', #{ammeterno}, '%')</if>
        <if test="isentityammeter != null">and pap.isentityammeter = #{isentityammeter}</if>
        <if test="oldAmmeterId != null">and pap.old_ammeter_id = #{oldAmmeterId}</if>
        <if test="ischangeammeter != null">and pap.ischangeammeter = #{ischangeammeter}</if>
        <if test="oldBillPower != null">and pap.old_bill_power like concat('%', #{oldBillPower}, '%')</if>
        <if test="ybgPower != null">and pap.ybg_power like concat('%', #{ybgPower}, '%')</if>
        <if test="lumpenddate != null">and pap.lumpenddate like concat('%', #{lumpenddate}, '%')</if>

        <!--        <if test="creatorId != null"> and pap.creator_id = #{creatorId}</if>-->
        <!--        <if test="updateById != null"> and pap.update_id = #{updateById}</if>-->
    </sql>

    <sql id="like-condition">
        <if test="id != null">and pap.id like concat('%', #{id}, '%')</if>
        <if test="category != null">and pap.category like concat('%', #{category}, '%')</if>
        <if test="ammetername != null">and pap.ammetername like concat('%', #{ammetername}, '%')</if>
        <if test="protocolname != null">and pap.protocolname like concat('%', #{protocolname}, '%')</if>
        <if test="country != null">and pap.country like concat('%', #{country}, '%')</if>
        <if test="company != null">and pap.company like concat('%', #{company}, '%')</if>
        <if test="substation != null">and pap.substation like concat('%', #{substation}, '%')</if>
        <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
        <if test="address != null">and pap.address like concat('%', #{address}, '%')</if>
        <if test="payname != null">and pap.payname like concat('%', #{payname}, '%')</if>
        <if test="paytype != null">and pap.paytype like concat('%', #{paytype}, '%')</if>
        <if test="payperiod != null">and pap.payperiod like concat('%', #{payperiod}, '%')</if>
        <if test="paymanager != null">and pap.paymanager like concat('%', #{paymanager}, '%')</if>
        <if test="ammetermanager != null">and pap.ammetermanager like concat('%', #{ammetermanager}, '%')</if>
        <if test="ammetertype != null">and pap.ammetertype like concat('%', #{ammetertype}, '%')</if>
        <if test="electrovalencenature != null">and pap.electrovalencenature like concat('%', #{electrovalencenature},
            '%')
        </if>
        <if test="electronature != null">and pap.electronature like concat('%', #{electronature}, '%')</if>
        <if test="electrotype != null">and pap.electrotype like concat('%', #{electrotype}, '%')</if>
        <if test="magnification != null">and pap.magnification like concat('%', #{magnification}, '%')</if>
        <if test="isStdStation != null">and pap.is_std_station like concat('%', #{isStdStation}, '%')</if>
        <if test="directsupplyflag != null">and pap.directsupplyflag like concat('%', #{directsupplyflag}, '%')</if>
        <if test="price != null">and pap.price like concat('%', #{price}, '%')</if>
        <if test="packagetype != null">and pap.packagetype like concat('%', #{packagetype}, '%')</if>
        <if test="outeruser != null">and pap.outeruser like concat('%', #{outeruser}, '%')</if>
        <if test="userunit != null">and pap.userunit like concat('%', #{userunit}, '%')</if>
        <if test="location != null">and pap.location like concat('%', #{location}, '%')</if>
        <if test="contractname != null">and pap.contractname like concat('%', #{contractname}, '%')</if>
        <if test="officephone != null">and pap.officephone like concat('%', #{officephone}, '%')</if>
        <if test="telephone != null">and pap.telephone like concat('%', #{telephone}, '%')</if>
        <if test="receiptaccountname != null">and pap.receiptaccountname like concat('%', #{receiptaccountname}, '%')</if>
        <if test="receiptaccountbank != null">and pap.receiptaccountbank like concat('%', #{receiptaccountbank}, '%')</if>
        <if test="receiptaccounts != null">and pap.receiptaccounts like concat('%', #{receiptaccounts}, '%')</if>
        <if test="protocolscan != null">and pap.protocolscan like concat('%', #{protocolscan}, '%')</if>
        <if test="protocolsigneddate != null">and pap.protocolsigneddate like concat('%', #{protocolsigneddate}, '%')</if>
        <if test="protocolterminatedate != null">and pap.protocolterminatedate like concat('%', #{protocolterminatedate},
            '%')
        </if>
        <if test="status != null">and pap.status like concat('%', #{status}, '%')</if>
        <if test="fee != null">and pap.fee like concat('%', #{fee}, '%')</if>
        <if test="wastageFlag != null">and pap.wastage_flag like concat('%', #{wastageFlag}, '%')</if>
        <if test="maxdegree != null">and pap.maxdegree like concat('%', #{maxdegree}, '%')</if>
        <if test="inputdate != null">and pap.inputdate like concat('%', #{inputdate}, '%')</if>
        <if test="inputuser != null">and pap.inputuser like concat('%', #{inputuser}, '%')</if>
        <if test="memo != null">and pap.memo like concat('%', #{memo}, '%')</if>
        <if test="substationid != null">and pap.substationid like concat('%', #{substationid}, '%')</if>
        <if test="mapname != null">and pap.mapname like concat('%', #{mapname}, '%')</if>
        <if test="sourcetype != null">and pap.sourcetype like concat('%', #{sourcetype}, '%')</if>
        <if test="sourceinvoicetype != null">and pap.sourceinvoicetype like concat('%', #{sourceinvoicetype}, '%')</if>
        <if test="supplytype != null">and pap.supplytype like concat('%', #{supplytype}, '%')</if>
        <if test="supplyinvoicetype != null">and pap.supplyinvoicetype like concat('%', #{supplyinvoicetype}, '%')</if>
        <if test="property != null">and pap.property like concat('%', #{property}, '%')</if>
        <if test="paymanagerTele != null">and pap.paymanager_tele like concat('%', #{paymanagerTele}, '%')</if>
        <if test="percent != null">and pap.percent like concat('%', #{percent}, '%')</if>
        <if test="iprocessinstid != null">and pap.iprocessinstid like concat('%', #{iprocessinstid}, '%')</if>
        <if test="finishTime != null">and pap.finish_time like concat('%', #{finishTime}, '%')</if>
        <if test="finishFlag != null">and pap.finish_flag like concat('%', #{finishFlag}, '%')</if>
        <if test="phyMac != null">and pap.phy_mac like concat('%', #{phyMac}, '%')</if>
        <if test="phyName != null">and pap.phy_name like concat('%', #{phyName}, '%')</if>
        <if test="jtStationid != null">and pap.jt_stationid like concat('%', #{jtStationid}, '%')</if>
        <if test="jtDeviceid != null">and pap.jt_deviceid like concat('%', #{jtDeviceid}, '%')</if>
        <if test="jtMonth != null">and pap.jt_month like concat('%', #{jtMonth}, '%')</if>
        <if test="jtType != null">and pap.jt_type like concat('%', #{jtType}, '%')</if>
        <if test="jtHousecode != null">and pap.jt_housecode like concat('%', #{jtHousecode}, '%')</if>
        <if test="jtStationname != null">and pap.jt_stationname like concat('%', #{jtStationname}, '%')</if>
        <if test="jtDevicename != null">and pap.jt_devicename like concat('%', #{jtDevicename}, '%')</if>
        <if test="jtHousename != null">and pap.jt_housename like concat('%', #{jtHousename}, '%')</if>
        <if test="jtPhyMac != null">and pap.jt_phy_mac like concat('%', #{jtPhyMac}, '%')</if>
        <if test="contractOthPart != null">and pap.contract_oth_part like concat('%', #{contractOthPart}, '%')</if>
        <if test="stationName != null">and pap.station_name like concat('%', #{stationName}, '%')</if>
        <if test="nmCcode != null">and pap.nm_ccode like concat('%', #{nmCcode}, '%')</if>
        <if test="nmL2100 != null">and pap.nm_l2100 like concat('%', #{nmL2100}, '%')</if>
        <if test="nmL1800 != null">and pap.nm_l1800 like concat('%', #{nmL1800}, '%')</if>
        <if test="nmCl800m != null">and pap.nm_cl800m like concat('%', #{nmCl800m}, '%')</if>
        <if test="islumpsum != null">and pap.islumpsum like concat('%', #{islumpsum}, '%')</if>
        <if test="lumpstartdate != null">and pap.lumpstartdate like concat('%', #{lumpstartdate}, '%')</if>
        <if test="lumprrunum != null">and pap.lumprrunum like concat('%', #{lumprrunum}, '%')</if>

        <if test="supplybureauammetercode != null">and pap.supplybureauammetercode like concat('%',
            #{supplybureauammetercode}, '%')
        </if>
        <if test="issmartammeter != null">and pap.issmartammeter like concat('%', #{issmartammeter}, '%')</if>
        <if test="supplybureauname != null">and pap.supplybureauname like concat('%', #{supplybureauname}, '%')</if>
        <if test="generationof != null">and pap.generationof like concat('%', #{generationof}, '%')</if>
        <if test="quotapowerratio != null">and pap.quotapowerratio like concat('%', #{quotapowerratio}, '%')</if>
        <if test="stationcode != null">and pap.stationcode like concat('%', #{stationcode}, '%')</if>
        <if test="stationstatus != null">and pap.stationstatus like concat('%', #{stationstatus}, '%')</if>
        <if test="stationtype != null">and pap.stationtype like concat('%', #{stationtype}, '%')</if>
        <if test="stationaddress != null">and pap.stationaddress like concat('%', #{stationaddress}, '%')</if>
        <if test="stationaddresscode != null">and pap.stationaddresscode like concat('%', #{stationaddresscode}, '%')</if>
        <if test="isairconditioning != null">and pap.isairconditioning like concat('%', #{isairconditioning}, '%')</if>
        <if test="vouchelectricity != null">and pap.vouchelectricity like concat('%', #{vouchelectricity}, '%')</if>

        <if test="billStatus != null">and pap.bill_status like concat('%', #{billStatus}, '%')</if>
        <if test="isAttach != null">and pap.is_attach like concat('%', #{isAttach}, '%')</if>
        <if test="ammeteruse != null">and pap.ammeteruse = #{ammeteruse}</if>
        <if test="ammeterno != null">and pap.ammeterno = #{ammeterno}</if>
        <if test="isentityammeter != null">and pap.isentityammeter = #{isentityammeter}</if>
        <if test="creatorId != null">and pap.creator_id = #{creatorId}</if>
        <if test="updateById != null">and pap.update_id = #{updateById}</if>
        <if test="oldAmmeterId != null">and pap.old_ammeter_id = #{oldAmmeterId}</if>
        <if test="ischangeammeter != null">and pap.ischangeammeter = #{ischangeammeter}</if>
        <if test="oldBillPower != null">and pap.old_bill_power like concat('%', #{oldBillPower}, '%')</if>
        <if test="ybgPower != null">and pap.ybg_power like concat('%', #{ybgPower}, '%')</if>
        <if test="lumpenddate != null">and pap.lumpenddate like concat('%', #{lumpenddate}, '%')</if>

        <if test="delFlag != null">and pap.del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>

    <select id="selectList" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        <include refid="selectVo"/>
        <where>
            pap.del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>
    <select id="selectByList" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        <include refid="selectAll"/>
    </select>
    <select id="selectByListLN" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        <include refid="selectAllLN"/>
    </select>

    <select id="selectByLike" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        <include refid="selectVo"/>
        <where>
            pap.del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="AmmeterorprotocolResult">
        <include refid="selectVo"/>
        <where>
            pap.del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="AmmeterorprotocolResult">
        <include refid="selectVo1"/>
        where pap.del_flag = '0' and pap.id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </select>

    <select id="count" parameterType="Ammeterorprotocol" resultType="Integer">
        select count(*) from power_ammeterorprotocol pap
        <where>
            pap.del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>
    <select id="countatt" parameterType="Long" resultType="Integer">
        select count(*) from attachments
        <where>
            busi_id=
            #{id}
        </where>
    </select>
    <insert id="insert" parameterType="Ammeterorprotocol" useGeneratedKeys="false" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            <if test="id == null">
                select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
            </if>
            <if test="id != null">
                select #{id} as id from dual
            </if>
        </selectKey>
        insert into power_ammeterorprotocol
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,category,ammetername,protocolname,country,company,substation,projectname,address,payname,paytype,payperiod,paymanager,ammetermanager,ammetertype,electrovalencenature,electronature,electrotype,magnification,is_std_station,directsupplyflag,price,packagetype,outeruser,userunit,location,contractname,officephone,telephone,receiptaccountname,receiptaccountbank,receiptaccounts,protocolscan,protocolsigneddate,protocolterminatedate,status,fee,wastage_flag,maxdegree,inputdate,inputuser,memo,substationid,mapname,sourcetype,sourceinvoicetype,supplytype,supplyinvoicetype,property,paymanager_tele,percent,iprocessinstid,finish_time,finish_flag,phy_mac,phy_name,jt_stationid,jt_deviceid,jt_month,jt_type,jt_housecode,jt_stationname,jt_devicename,jt_housename,jt_phy_mac,contract_oth_part,station_name,nm_ccode,nm_l2100,nm_l1800,nm_cl800m,islumpsum,lumpstartdate,lumprrunum,supplybureauammetercode,issmartammeter,supplybureauname,generationof,quotapowerratio,stationcode,stationstatus,stationtype,stationaddress,stationaddresscode,isairconditioning,vouchelectricity,bill_status,ammeteruse,ammeterno,isentityammeter,create_time,update_time,del_flag,customer_id,
            creator_id,update_id,parent_id,old_ammeter_id,ischangeammeter,old_bill_power,ybg_power,lumpenddate,iszgz,oldammetername,voltageClass,iszg,isbg,transdistricompany,
            TransferPowerSupplyContractCode,TransferPowerSupplyContractPrice,transfercontractname
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id}, #{category}, #{ammetername}, #{protocolname}, #{country}, #{company}, #{substation}, #{projectname},
            #{address}, #{payname}, #{paytype}, #{payperiod}, #{paymanager}, #{ammetermanager}, #{ammetertype},
            #{electrovalencenature}, #{electronature}, #{electrotype}, #{magnification}, #{isStdStation},
            #{directsupplyflag}, #{price}, #{packagetype}, #{outeruser}, #{userunit}, #{location}, #{contractname},
            #{officephone}, #{telephone}, #{receiptaccountname}, #{receiptaccountbank}, #{receiptaccounts},
            #{protocolscan}, #{protocolsigneddate}, #{protocolterminatedate}, #{status}, #{fee}, #{wastageFlag},
            #{maxdegree}, #{inputdate}, #{inputuser}, #{memo}, #{substationid}, #{mapname}, #{sourcetype},
            #{sourceinvoicetype}, #{supplytype}, #{supplyinvoicetype}, #{property}, #{paymanagerTele}, #{percent},
            #{iprocessinstid}, #{finishTime}, #{finishFlag}, #{phyMac}, #{phyName}, #{jtStationid}, #{jtDeviceid},
            #{jtMonth}, #{jtType}, #{jtHousecode}, #{jtStationname}, #{jtDevicename}, #{jtHousename}, #{jtPhyMac},
            #{contractOthPart}, #{stationName}, #{nmCcode}, #{nmL2100}, #{nmL1800}, #{nmCl800m}, #{islumpsum},
            #{lumpstartdate}, #{lumprrunum},
            #{supplybureauammetercode},#{issmartammeter},#{supplybureauname},#{generationof},#{quotapowerratio},
            #{stationcode},#{stationstatus},#{stationtype},#{stationaddress},#{stationaddresscode},#{isairconditioning},
            #{vouchelectricity},#{billStatus},#{ammeteruse},#{ammeterno},#{isentityammeter},
            (select SYSDATE() from dual),(select SYSDATE() from
            dual),'0',#{customerId},#{creatorId},#{updateById},#{parentId},
            #{oldAmmeterId},#{ischangeammeter},#{oldBillPower},#{ybgPower},#{lumpenddate},#{iszgz},#{oldammetername},#{voltageClass},#{directFlag},#{officeFlag},#{transdistricompany},
            #{TransferPowerSupplyContractCode},#{TransferPowerSupplyContractPrice},#{transfercontractname}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into power_ammeterorprotocol
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            category,
            ammetername,
            protocolname,
            country,
            company,
            substation,
            projectname,
            address,
            payname,
            paytype,
            payperiod,
            paymanager,
            ammetermanager,
            ammetertype,
            electrovalencenature,
            electronature,
            electrotype,
            magnification,
            is_std_station,
            directsupplyflag,
            price,
            packagetype,
            outeruser,
            userunit,
            location,
            contractname,
            officephone,
            telephone,
            receiptaccountname,
            receiptaccountbank,
            receiptaccounts,
            protocolscan,
            protocolsigneddate,
            protocolterminatedate,
            status,
            fee,
            wastage_flag,
            maxdegree,
            inputdate,
            inputuser,
            memo,
            substationid,
            mapname,
            sourcetype,
            sourceinvoicetype,
            supplytype,
            supplyinvoicetype,
            property,
            paymanager_tele,
            percent,
            iprocessinstid,
            finish_time,
            finish_flag,
            phy_mac,
            phy_name,
            jt_stationid,
            jt_deviceid,
            jt_month,
            jt_type,
            jt_housecode,
            jt_stationname,
            jt_devicename,
            jt_housename,
            jt_phy_mac,
            contract_oth_part,
            station_name,
            nm_ccode,
            nm_l2100,
            nm_l1800,
            nm_cl800m,
            islumpsum,
            lumpstartdate,
            lumprrunum,
            supplybureauammetercode,
            issmartammeter,
            supplybureauname,
            generationof,
            quotapowerratio,
            stationcode,
            stationstatus,
            stationtype,
            stationaddress,
            stationaddresscode,
            isairconditioning,
            vouchelectricity,
            bill_status,
            ammeteruse,
            ammeterno,
            isentityammeter,
            create_time,
            del_flag,
            parent_id,
            customer_id,
            creator_id,
            update_id,
            old_ammeter_id,
            ischangeammeter,
            old_bill_power,
            ybg_power,
            lumpenddate,
            iszgz,
            oldammetername,
            TransferPowerSupplyContractCode,
            TransferPowerSupplyContractPrice,
            transfercontractname
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.category},
                #{item.ammetername},
                #{item.protocolname},
                #{item.country},
                #{item.company},
                #{item.substation},
                #{item.projectname},
                #{item.address},
                #{item.payname},
                #{item.paytype},
                #{item.payperiod},
                #{item.paymanager},
                #{item.ammetermanager},
                #{item.ammetertype},
                #{item.electrovalencenature},
                #{item.electronature},
                #{item.electrotype},
                #{item.magnification},
                #{item.isStdStation},
                #{item.directsupplyflag},
                #{item.price},
                #{item.packagetype},
                #{item.outeruser},
                #{item.userunit},
                #{item.location},
                #{item.contractname},
                #{item.officephone},
                #{item.telephone},
                #{item.receiptaccountname},
                #{item.receiptaccountbank},
                #{item.receiptaccounts},
                #{item.protocolscan},
                #{item.protocolsigneddate},
                #{item.protocolterminatedate},
                #{item.status},
                #{item.fee},
                #{item.wastageFlag},
                #{item.maxdegree},
                #{item.inputdate},
                #{item.inputuser},
                #{item.memo},
                #{item.substationid},
                #{item.mapname},
                #{item.sourcetype},
                #{item.sourceinvoicetype},
                #{item.supplytype},
                #{item.supplyinvoicetype},
                #{item.property},
                #{item.paymanagerTele},
                #{item.percent},
                #{item.iprocessinstid},
                #{item.finishTime},
                #{item.finishFlag},
                #{item.phyMac},
                #{item.phyName},
                #{item.jtStationid},
                #{item.jtDeviceid},
                #{item.jtMonth},
                #{item.jtType},
                #{item.jtHousecode},
                #{item.jtStationname},
                #{item.jtDevicename},
                #{item.jtHousename},
                #{item.jtPhyMac},
                #{item.contractOthPart},
                #{item.stationName},
                #{item.nmCcode},
                #{item.nmL2100},
                #{item.nmL1800},
                #{item.nmCl800m},
                #{item.islumpsum},
                #{item.lumpstartdate},
                #{item.lumprrunum},
                #{item.supplybureauammetercode},
                #{item.issmartammeter},
                #{item.supplybureauname},
                #{item.generationof},
                #{item.quotapowerratio},
                #{item.stationcode},
                #{item.stationstatus},
                #{item.stationtype},
                #{item.stationaddress},
                #{item.stationaddresscode},
                #{item.isairconditioning},
                #{item.vouchelectricity},
                #{item.billStatus},
                #{item.ammeteruse},
                #{item.ammeterno},
                #{item.isentityammeter},
                #{item.createTime},
                '0',
                #{item.parentId},
                #{item.customerId},
                #{item.creatorId},
                #{item.updateById},
                #{item.oldAmmeterId},
                #{item.ischangeammeter},
                #{item.oldBillPower},
                #{item.ybgPower},
                #{item.lumpenddate},
                #{item.TransferPowerSupplyContractCode},
                #{item.TransferPowerSupplyContractPrice},
                #{item.transfercontractname}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="Ammeterorprotocol">
        update power_ammeterorprotocol
        <trim prefix="SET" suffixOverrides=",">
            category = #{category},
            ammetername = #{ammetername},
            protocolname = #{protocolname},
            country = #{country},
            company = #{company},
            substation = #{substation},
            projectname = #{projectname},
            address = #{address},
            payname = #{payname},
            paytype = #{paytype},
            payperiod = #{payperiod},
            paymanager = #{paymanager},
            ammetermanager = #{ammetermanager},
            ammetertype = #{ammetertype},
            electrovalencenature = #{electrovalencenature},
            electronature = #{electronature},
            electrotype = #{electrotype},
            magnification = #{magnification},
            is_std_station = #{isStdStation},
            directsupplyflag = #{directsupplyflag},
            price = #{price},
            packagetype = #{packagetype},
            outeruser = #{outeruser},
            userunit = #{userunit},
            location = #{location},
            contractname = #{contractname},
            officephone = #{officephone},
            telephone = #{telephone},
            receiptaccountname = #{receiptaccountname},
            receiptaccountbank = #{receiptaccountbank},
            receiptaccounts = #{receiptaccounts},
            protocolscan = #{protocolscan},
            protocolsigneddate = #{protocolsigneddate},
            protocolterminatedate = #{protocolterminatedate},
            status = #{status},
            fee = #{fee},
            wastage_flag = #{wastageFlag},
            maxdegree = #{maxdegree},
            inputdate = #{inputdate},
            inputuser = #{inputuser},
            memo = #{memo},
            substationid = #{substationid},
            mapname = #{mapname},
            sourcetype = #{sourcetype},
            sourceinvoicetype = #{sourceinvoicetype},
            supplytype = #{supplytype},
            supplyinvoicetype = #{supplyinvoicetype},
            property = #{property},
            paymanager_tele = #{paymanagerTele},
            percent = #{percent},
            iprocessinstid = #{iprocessinstid},
            finish_time = #{finishTime},
            finish_flag = #{finishFlag},
            phy_mac = #{phyMac},
            phy_name = #{phyName},
            jt_stationid = #{jtStationid},
            jt_deviceid = #{jtDeviceid},
            jt_month = #{jtMonth},
            jt_type = #{jtType},
            jt_housecode = #{jtHousecode},
            jt_stationname = #{jtStationname},
            jt_devicename = #{jtDevicename},
            jt_housename = #{jtHousename},
            jt_phy_mac = #{jtPhyMac},
            contract_oth_part = #{contractOthPart},
            station_name = #{stationName},
            nm_ccode = #{nmCcode},
            nm_l2100 = #{nmL2100},
            nm_l1800 = #{nmL1800},
            nm_cl800m = #{nmCl800m},
            islumpsum = #{islumpsum},
            lumpstartdate = #{lumpstartdate},
            supplybureauammetercode = #{supplybureauammetercode},
            issmartammeter = #{issmartammeter},
            supplybureauname = #{supplybureauname},
            generationof = #{generationof},
            quotapowerratio = #{quotapowerratio},
            stationcode = #{stationcode},
            stationstatus = #{stationstatus},
            stationtype = #{stationtype},
            stationaddress = #{stationaddress},
            stationaddresscode = #{stationaddresscode},
            isairconditioning = #{isairconditioning},
            vouchelectricity = #{vouchelectricity},
            bill_status = #{billStatus},
            ammeteruse = #{ammeteruse},
            ammeterno = #{ammeterno},
            isentityammeter = #{isentityammeter},
            del_flag = #{delFlag},
            parent_id = #{parentId},
            customer_id = #{customerId},
            update_time = (select SYSDATE() from dual),
            creator_id = #{creatorId},
            update_id = #{updateById},
            old_ammeter_id = #{oldAmmeterId},
            ischangeammeter = #{ischangeammeter},
            old_bill_power = #{oldBillPower},
            ybg_power = #{ybgPower},
            lumpenddate = #{lumpenddate},
            iszgz = #{iszgz},
            oldammetername =
            #{oldammetername},voltageClass=#{voltageClass},iszg=#{directFlag},isbg=#{officeFlag},transdistricompany=#{transdistricompany},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="Ammeterorprotocol">
        update power_ammeterorprotocol
        <trim prefix="SET" suffixOverrides=",">
            <if test="category != null  ">category = #{category},</if>
            <if test="ammetername != null  and ammetername != ''  ">ammetername = #{ammetername},</if>
            <if test="protocolname != null  and protocolname != ''  ">protocolname = #{protocolname},</if>
            <if test="country != null  ">country = #{country},</if>
            <if test="company != null  ">company = #{company},</if>
            <if test="substation != null  and substation != ''  ">substation = #{substation},</if>
            <if test="projectname != null  and projectname != ''  ">projectname = #{projectname},</if>
            <if test="address != null  and address != ''  ">address = #{address},</if>
            <if test="payname != null  and payname != ''  ">payname = #{payname},</if>
            <if test="paytype != null  ">paytype = #{paytype},</if>
            <if test="payperiod != null  ">payperiod = #{payperiod},</if>
            <if test="paymanager != null  and paymanager != ''  ">paymanager = #{paymanager},</if>
            <if test="ammetermanager != null  and ammetermanager != ''  ">ammetermanager = #{ammetermanager},</if>
            <if test="ammetertype != null  ">ammetertype = #{ammetertype},</if>
            <if test="electrovalencenature != null  ">electrovalencenature = #{electrovalencenature},</if>
            <if test="electronature != null  ">electronature = #{electronature},</if>
            <if test="electrotype != null  ">electrotype = #{electrotype},</if>
            <if test="magnification != null  ">magnification = #{magnification},</if>
            <if test="isStdStation != null  ">is_std_station = #{isStdStation},</if>
            <if test="directsupplyflag != null  ">directsupplyflag = #{directsupplyflag},</if>
            <if test="price != null  ">price = #{price},</if>
            <if test="packagetype != null  ">packagetype = #{packagetype},</if>
            <if test="outeruser != null  and outeruser != ''  ">outeruser = #{outeruser},</if>
            <if test="userunit != null  and userunit != ''  ">userunit = #{userunit},</if>
            <if test="location != null  and location != ''  ">location = #{location},</if>
            <if test="contractname != null  and contractname != ''  ">contractname = #{contractname},</if>
            <if test="officephone != null  and officephone != ''  ">officephone = #{officephone},</if>
            <if test="telephone != null  and telephone != ''  ">telephone = #{telephone},</if>
            <if test="receiptaccountname != null  and receiptaccountname != ''  ">receiptaccountname =
                #{receiptaccountname},
            </if>
            <if test="receiptaccountbank != null  and receiptaccountbank != ''  ">receiptaccountbank =
                #{receiptaccountbank},
            </if>
            <if test="receiptaccounts != null  and receiptaccounts != ''  ">receiptaccounts = #{receiptaccounts},</if>
            <if test="protocolscan != null  and protocolscan != ''  ">protocolscan = #{protocolscan},</if>
            <if test="protocolsigneddate != null  ">protocolsigneddate = #{protocolsigneddate},</if>
            <if test="protocolterminatedate != null  ">protocolterminatedate = #{protocolterminatedate},</if>
            <if test="status != null  ">status = #{status},</if>
            <if test="fee != null  ">fee = #{fee},</if>
            <if test="wastageFlag != null  ">wastage_flag = #{wastageFlag},</if>
            <if test="maxdegree != null  ">maxdegree = #{maxdegree},</if>
            <if test="inputdate != null  ">inputdate = #{inputdate},</if>
            <if test="inputuser != null  ">inputuser = #{inputuser},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
            <if test="substationid != null  ">substationid = #{substationid},</if>
            <if test="mapname != null  and mapname != ''  ">mapname = #{mapname},</if>
            <if test="sourcetype != null  ">sourcetype = #{sourcetype},</if>
            <if test="sourceinvoicetype != null  ">sourceinvoicetype = #{sourceinvoicetype},</if>
            <if test="supplytype != null  ">supplytype = #{supplytype},</if>
            <if test="supplyinvoicetype != null  ">supplyinvoicetype = #{supplyinvoicetype},</if>
            <if test="property != null  ">property = #{property},</if>
            <if test="paymanagerTele != null  and paymanagerTele != ''  ">paymanager_tele = #{paymanagerTele},</if>
            <if test="percent != null  ">percent = #{percent},</if>
            <if test="iprocessinstid != null  ">iprocessinstid = #{iprocessinstid},</if>
            <if test="finishTime != null  ">finish_time = #{finishTime},</if>
            <if test="finishFlag != null  ">finish_flag = #{finishFlag},</if>
            <if test="phyMac != null  and phyMac != ''  ">phy_mac = #{phyMac},</if>
            <if test="phyName != null  and phyName != ''  ">phy_name = #{phyName},</if>
            <if test="jtStationid != null  and jtStationid != ''  ">jt_stationid = #{jtStationid},</if>
            <if test="jtDeviceid != null  and jtDeviceid != ''  ">jt_deviceid = #{jtDeviceid},</if>
            <if test="jtMonth != null  ">jt_month = #{jtMonth},</if>
            <if test="jtType != null  ">jt_type = #{jtType},</if>
            <if test="jtHousecode != null  and jtHousecode != ''  ">jt_housecode = #{jtHousecode},</if>
            <if test="jtStationname != null  and jtStationname != ''  ">jt_stationname = #{jtStationname},</if>
            <if test="jtDevicename != null  and jtDevicename != ''  ">jt_devicename = #{jtDevicename},</if>
            <if test="jtHousename != null  and jtHousename != ''  ">jt_housename = #{jtHousename},</if>
            <if test="jtPhyMac != null  and jtPhyMac != ''  ">jt_phy_mac = #{jtPhyMac},</if>
            <if test="contractOthPart != null  and contractOthPart != ''  ">contract_oth_part = #{contractOthPart},</if>
            <if test="stationName != null  and stationName != ''  ">station_name = #{stationName},</if>
            <if test="nmCcode != null  and nmCcode != ''  ">nm_ccode = #{nmCcode},</if>
            <if test="nmL2100 != null  and nmL2100 != ''  ">nm_l2100 = #{nmL2100},</if>
            <if test="nmL1800 != null  and nmL1800 != ''  ">nm_l1800 = #{nmL1800},</if>
            <if test="nmCl800m != null  and nmCl800m != ''  ">nm_cl800m = #{nmCl800m},</if>
            <if test="islumpsum != null  ">islumpsum = #{islumpsum},</if>
            <if test="lumpstartdate != null  ">lumpstartdate = #{lumpstartdate},</if>
            <if test="lumprrunum != null  ">lumprrunum = #{lumprrunum},</if>
            <if test="supplybureauammetercode != null  ">supplybureauammetercode = #{supplybureauammetercode},</if>
            <if test="issmartammeter != null  ">issmartammeter = #{issmartammeter},</if>
            <if test="supplybureauname != null  ">supplybureauname = #{supplybureauname},</if>
            <if test="generationof != null  ">generationof = #{generationof},</if>
            <if test="quotapowerratio != null  ">quotapowerratio = #{quotapowerratio},</if>
            <if test="stationcode != null  ">stationcode = #{stationcode},</if>
            <if test="stationstatus != null  ">stationstatus = #{stationstatus},</if>
            <if test="stationtype != null  ">stationtype = #{stationtype},</if>
            <if test="stationaddress != null  ">stationaddress = #{stationaddress},</if>
            <if test="stationaddresscode != null  ">stationaddresscode = #{stationaddresscode},</if>
            <if test="isairconditioning != null  ">isairconditioning = #{isairconditioning},</if>
            <if test="vouchelectricity != null  ">vouchelectricity = #{vouchelectricity},</if>
            <if test="billStatus != null  ">bill_status = #{billStatus},</if>
            <if test="ammeteruse != null">ammeteruse = #{ammeteruse},</if>
            <if test="ammeterno != null">ammeterno = #{ammeterno},</if>
            <if test="isentityammeter != null">isentityammeter = #{isentityammeter},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="parentId != null  ">parent_id = #{parentId},</if>
            <if test="customerId != null  ">customer_id = #{customerId},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="updateTime == null ">update_time = (select SYSDATE() from dual),</if>
            <if test="creatorId != null ">creator_id = #{creatorId},</if>
            <if test="updateById != null ">update_id = #{updateById},</if>
            <if test="oldAmmeterId != null">old_ammeter_id = #{oldAmmeterId},</if>
            <if test="ischangeammeter != null">ischangeammeter = #{ischangeammeter},</if>
            <if test="oldBillPower != null">old_bill_power = #{oldBillPower},</if>
            <if test="ybgPower != null">ybg_power = #{ybgPower},</if>
            <if test="lumpenddate != null">lumpenddate = #{lumpenddate},</if>
            <if test="iszgz != null">iszgz = #{iszgz},</if>
            <if test="oldammetername != null">oldammetername = #{oldammetername},</if>
            <if test="voltageClass != null">voltageClass=#{voltageClass},</if>
            <if test="directFlag != null">iszg=#{directFlag},</if>
            <if test="officeFlag != null">isbg=#{officeFlag},</if>
            <if test="transdistricompany != null">transdistricompany=#{transdistricompany},</if>

        </trim>
        where id = #{id}
    </update>
    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE power_ammeterorprotocol SET DEL_FLAG='1' where id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE power_ammeterorprotocol SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from power_ammeterorprotocol where id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from power_ammeterorprotocol where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMeterIfo">
        truncate meterinfo;
    </delete>
    <delete id="deleteMeterIfoTwoc">
        truncate meterinfo_twoc;
    </delete>
    <!-- 获取有效的电表/协议
	 sql 条件
	 数据未删除
	 类型为电表或者协议为支出类有表协议
	 未新增过定额
	 用户角色
	 在用
	 单据状态不是草稿和流程中
	  -->
    <select id="getAmmeterProtocolList" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        select pap.id,pap.protocolname,pap.ammetername,
        concat((case when country1.org_name is not null then concat(country1.org_name,'-') else ''
        end),country.org_name) countryName,
        pap.country, pap.company, pap.projectname,pap.category from power_ammeterorprotocol pap
        LEFT JOIN (select org.id,org.org_name,PARENT_COMPANY_NO parent_id from rmp.sys_organizations org) country on
        country.id = pap.country
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) country1 on country1.id =
        country.parent_id and country1.id !=pap.company
        <where>
            pap.del_flag = '0' and pap.`status`=1 AND pap.bill_status BETWEEN 2 and 4
            AND (pap.category = 1 OR pap.category = 3 )
            and (SELECT count(1) as num from power_quota pq where pq.device_id=pap.id and del_flag = 0)=0
            <if test="ammetername != null">and (pap.ammetername like concat('%', #{ammetername}, '%') or
                pap.protocolname like concat('%', #{ammetername}, '%'))
            </if>
            <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
            <if test="country != null">and pap.country = #{country}</if>
            <if test="company != null">and pap.company = #{company}</if>
            <if test="countrys.size != 0">
                and pap.country in
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <foreach collection="countrys" item="item" index="index" separator=",">
                        #{item.id}
                    </foreach>
                </trim>
            </if>
        </where>
        ORDER BY pap.update_time is null,pap.update_time,pap.create_time desc
    </select>
    <select id="getParentAmmeterProtocolList" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        select pap.id,pap.protocolname,pap.ammetername,
        concat((case when country1.org_name is not null then concat(country1.org_name,'-') else ''
        end),country.org_name) countryName,
        pap.country, pap.company, pap.projectname,pap.category from power_ammeterorprotocol pap
        LEFT JOIN (select org.id,org.org_name,PARENT_COMPANY_NO parent_id from rmp.sys_organizations org) country on
        country.id = pap.country
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) country1 on country1.id =
        country.parent_id and country1.id !=pap.company
        <where>
            pap.del_flag = '0' and pap.`status`=1 AND pap.bill_status BETWEEN 2 and 4
            and ((pap.category=1 and pap.ammeteruse = 1) or pap.category = 2 OR pap.category=3)
            <if test="ammetername != null">and (pap.ammetername like concat('%', #{ammetername}, '%') or
                pap.protocolname like concat('%', #{ammetername}, '%'))
            </if>
            <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
            <if test="country != null">and pap.country = #{country}</if>
            <if test="company != null">and pap.company = #{company}</if>
            <if test="id != null">and pap.id != #{id}</if>
            <if test="countrys.size != 0">
                and pap.country in
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <foreach collection="countrys" item="item" index="index" separator=",">
                        #{item.id}
                    </foreach>
                </trim>
            </if>
        </where>
        order by pap.update_time is null,pap.update_time,pap.create_time desc
    </select>

    <select id="selectAmmeterListByPUE" parameterType="Map" resultMap="AmmeterorprotocolResult">
        select pap.id,pap.protocolname,pap.ammetername,sorg.org_name countryName,
        pap.country, pap.company, pap.projectname,pap.category from power_ammeterorprotocol pap
        LEFT JOIN rmp.sys_organizations sorg ON sorg.id = pap.country
        <where>
            pap.del_flag = '0' and pap.`status`=1 AND pap.bill_status BETWEEN 2 and 4
            <if test="ammetername != null">and (pap.ammetername like concat('%', #{ammetername}, '%') or
                pap.protocolname like concat('%', #{ammetername}, '%'))
            </if>
            <if test="projectname != null">and pap.projectname like concat('%', #{projectname}, '%')</if>
            <if test="company != null">and pap.company = #{company}</if>
            <if test="id != null">and pap.id != #{id}</if>
            and pap.id not in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="ids" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </where>
        ORDER BY pap.update_time is null,pap.update_time,pap.create_time desc
    </select>

    <select id="selectListBy" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        select eto1.org_name as companyname,eto2.org_name as countryname,a.protocolname ,a.projectname,
        (select b.type_name from power_category_type b where a.category=b.type_code and
        b.type_category='ammeterCategory') as categoryname,
        (select b.type_name from power_electric_classification b where a.electrotype=b.id) as electrotypename,
        (select b.type_name from power_category_type b where a.electronature=b.type_code and
        b.type_category='electroNature') as electronaturename,
        (select b.type_name from power_category_type b where a.paytype=b.type_code and b.type_category='payType') as
        paytypename,
        (select b.type_name from power_category_type b where a.payperiod=b.type_code and b.type_category='payPeriod') as
        payperiodname,
        a.paymanager,
        (select b.type_name from power_category_type b where a.packagetype=b.type_code and
        b.type_category='packageType') as packagetypename,
        date_format(a.protocolsigneddate,'%Y-%m-%d') as protocolsigneddate,
        date_format(a.protocolterminatedate,'%Y-%m-%d') as protocolterminatedate
        from power_ammeterorprotocol a left join rmp.sys_organizations eto1
        on a.company = eto1.id left join rmp.sys_organizations eto2 on a.country = eto2.id
        <where>
            a.del_flag = '0'
            and date_format(sysdate(),'%Y/%m/%d') >= date_format(DATE_SUB(a.protocolterminatedate,INTERVAL 1
            month),'%Y/%m/%d')
            and category !=1
            <if test="company != null and company != ''">
                and a.company=#{company}
            </if>
            <if test="country != null and country != '' and country!=-1">
                and a.country=#{country}
            </if>
        </where>
    </select>

    <select id="getAmmeterorProtocolCategory" resultType="Map">
        SELECT
        '机楼(机房)' type_name,count(1) cnt
        FROM
        power_ammeterorprotocol a
        where a.status=1 and a.electrotype in(111,112,113)
        <if test="companies != null and companies.size()>0">
            and a.company in
            <foreach collection="companies" item="item" index="index" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
        union ALL
        SELECT
        '数据中心' type_name,count(1) cnt
        FROM
        power_ammeterorprotocol a
        where a.status=1 and a.electrotype in(121,122)
        <if test="companies != null and companies.size()>0">
            and a.company in
            <foreach collection="companies" item="item" index="index" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
        union ALL
        SELECT
        '接入机房' type_name,count(1) cnt
        FROM
        power_ammeterorprotocol a
        where a.status=1 and a.electrotype in(131,132,133)
        <if test="companies != null and companies.size()>0">
            and a.company in
            <foreach collection="companies" item="item" index="index" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
        union ALL
        SELECT
        '移动基站' type_name,count(1) cnt
        FROM
        power_ammeterorprotocol a
        where a.status=1 and a.electrotype in(1411,1412,1421,1422,1431,1432)
        <if test="companies != null and companies.size()>0">
            and a.company in
            <foreach collection="companies" item="item" index="index" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
        union ALL
        SELECT '非生产' type_name,count(1) cnt
        FROM power_ammeterorprotocol a where a.status=1 and a.electrotype in(2,4,31,32,33)
        <if test="companies != null and companies.size()>0">
            and a.company in
            <foreach collection="companies" item="item" index="index" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
    </select>
    <select id="getstationAountmoneydataold" parameterType="String" resultType="Map">
        select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
               round(sum(pa.accountmoney) / 10000, 2)      money,
               round(sum(pa.totalusedreadings) / 10000, 2) readings
        from power_ammeterorprotocol pp
                 left join power_account pa on pp.id = pa.ammeterid
                 left join mss_r_billitem_account r on r.account_id = pa.pcid
                 left join mss_accountbill ma on ma.id = r.bill_id
        where ma.year = 2023
          and ma.status = 7
          and ma.COMPANY_CODE = #{company}
          and pp.electrotype in (1411, 1412, 1422, 1421, 1431, 1432)
        group by ma.BIZ_ENTRY_CODE
    </select>
    <select id="getstationAountmoneydataprov" parameterType="String" resultType="Map">
        select ta.*, sf.smoney, sf.sreadings, zy.zmoney, zy.zreadings
        from (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      money,
                     round(sum(pa.totalusedreadings) / 10000, 2) readings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7

                and pp.electrotype in (1411, 1412)
              group by ma.BIZ_ENTRY_CODE
             ) ta
                 left join

             (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      smoney,
                     round(sum(pa.totalusedreadings) / 10000, 2) sreadings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7

                and pp.electrotype in (1421, 1422)
              group by ma.BIZ_ENTRY_CODE) sf
             on ta.mont = sf.mont
                 left join (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                                   round(sum(pa.accountmoney) / 10000, 2)      zmoney,
                                   round(sum(pa.totalusedreadings) / 10000, 2) zreadings
                            from power_ammeterorprotocol pp
                                     left join power_account pa on pp.id = pa.ammeterid
                                     left join mss_r_billitem_account r on r.account_id = pa.pcid
                                     left join mss_accountbill ma on ma.id = r.bill_id
                            where ma.year = 2023
                              and ma.status = 7

                              and pp.electrotype in (1431, 1432)
                            group by ma.BIZ_ENTRY_CODE) zy on ta.mont = zy.mont
    </select>
    <select id="getstationAountmoneydatacity" parameterType="String" resultType="Map">
        select ta.*, sf.smoney, sf.sreadings, zy.zmoney, zy.zreadings
        from (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      money,
                     round(sum(pa.totalusedreadings) / 10000, 2) readings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7
                and ma.COMPANY_CODE = #{company}
                and pp.electrotype in (1411, 1412)
              group by ma.BIZ_ENTRY_CODE
             ) ta
                 left join

             (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      smoney,
                     round(sum(pa.totalusedreadings) / 10000, 2) sreadings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7
                and ma.COMPANY_CODE = #{company}
                and pp.electrotype in (1421, 1422)
              group by ma.BIZ_ENTRY_CODE) sf
             on ta.mont = sf.mont
                 left join (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                                   round(sum(pa.accountmoney) / 10000, 2)      zmoney,
                                   round(sum(pa.totalusedreadings) / 10000, 2) zreadings
                            from power_ammeterorprotocol pp
                                     left join power_account pa on pp.id = pa.ammeterid
                                     left join mss_r_billitem_account r on r.account_id = pa.pcid
                                     left join mss_accountbill ma on ma.id = r.bill_id
                            where ma.year = 2023
                              and ma.status = 7
                              and ma.COMPANY_CODE = #{company}
                              and pp.electrotype in (1431, 1432)
                            group by ma.BIZ_ENTRY_CODE) zy on ta.mont = zy.mont
    </select>
    <select id="getstationAountmoneydatacountry" parameterType="String" resultType="Map">
        select ta.*, sf.smoney, sf.sreadings, zy.zmoney, zy.zreadings
        from (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      money,
                     round(sum(pa.totalusedreadings) / 10000, 2) readings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7
                and ma.orgid = #{company}
                and pp.electrotype in (1411, 1412)
              group by ma.BIZ_ENTRY_CODE
             ) ta
                 left join

             (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                     round(sum(pa.accountmoney) / 10000, 2)      smoney,
                     round(sum(pa.totalusedreadings) / 10000, 2) sreadings
              from power_ammeterorprotocol pp
                       left join power_account pa on pp.id = pa.ammeterid
                       left join mss_r_billitem_account r on r.account_id = pa.pcid
                       left join mss_accountbill ma on ma.id = r.bill_id
              where ma.year = 2023
                and ma.status = 7
                and ma.orgid = #{company}
                and pp.electrotype in (1421, 1422)
              group by ma.BIZ_ENTRY_CODE) sf
             on ta.mont = sf.mont
                 left join (select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
                                   round(sum(pa.accountmoney) / 10000, 2)      zmoney,
                                   round(sum(pa.totalusedreadings) / 10000, 2) zreadings
                            from power_ammeterorprotocol pp
                                     left join power_account pa on pp.id = pa.ammeterid
                                     left join mss_r_billitem_account r on r.account_id = pa.pcid
                                     left join mss_accountbill ma on ma.id = r.bill_id
                            where ma.year = 2023
                              and ma.status = 7
                              and ma.orgid = #{company}
                              and pp.electrotype in (1431, 1432)
                            group by ma.BIZ_ENTRY_CODE) zy on ta.mont = zy.mont
    </select>
    <select id="getstationAountmoneytadata" parameterType="String" resultType="Map">
        select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
               round(sum(pa.accountmoney) / 10000, 2)      money,
               round(sum(pa.totalusedreadings) / 10000, 2) readings
        from power_ammeterorprotocol pp
                 left join power_account pa on pp.id = pa.ammeterid
                 left join mss_r_billitem_account r on r.account_id = pa.pcid
                 left join mss_accountbill ma on ma.id = r.bill_id
        where ma.year = 2022
          and ma.status = 7
          and ma.COMPANY_CODE = #{company}
          and pp.electrotype in (1411, 1412)
        group by ma.BIZ_ENTRY_CODE
    </select>
    <select id="getstationAountmoneysfdata" parameterType="String" resultType="Map">
        select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
               round(sum(pa.accountmoney) / 10000, 2)      money,
               round(sum(pa.totalusedreadings) / 10000, 2) readings
        from power_ammeterorprotocol pp
                 left join power_account pa on pp.id = pa.ammeterid
                 left join mss_r_billitem_account r on r.account_id = pa.pcid
                 left join mss_accountbill ma on ma.id = r.bill_id
        where ma.year = 2022
          and ma.status = 7
          and ma.COMPANY_CODE = #{company}
          and pp.electrotype in (1421, 1422)
        group by ma.BIZ_ENTRY_CODE
    </select>
    <select id="getstationAountmoneyzydata" parameterType="String" resultType="Map">
        select concat(ma.BIZ_ENTRY_CODE, '月')              mont,
               round(sum(pa.accountmoney) / 10000, 2)      money,
               round(sum(pa.totalusedreadings) / 10000, 2) readings
        from power_ammeterorprotocol pp
                 left join power_account pa on pp.id = pa.ammeterid
                 left join mss_r_billitem_account r on r.account_id = pa.pcid
                 left join mss_accountbill ma on ma.id = r.bill_id
        where ma.year = 2022
          and ma.status = 7
          and ma.COMPANY_CODE = #{company}
          and pp.electrotype in (1431, 1432)
        group by ma.BIZ_ENTRY_CODE
    </select>
    <select id="getAountmoneyCategory" parameterType="String" resultType="Map">
        SELECT '机楼(机房)'                       type_name,
               round(sum(amountYear) / 10000) cnt
        FROM statistical_amountmoney
        where year = YEAR(NOW())
          and billtype = -1
          and month = MONTH(NOW()) - 1
          and electrotype in (111, 112, 113)
          and company = #{company}
        union ALL
        SELECT '数据中心'                         type_name,
               round(sum(amountYear) / 10000) cnt
        FROM statistical_amountmoney
        where year = YEAR(NOW())
          and billtype = -1
          and month = MONTH(NOW()) - 1
          and electrotype in (121, 122)

          and company = #{company}

        union ALL
        SELECT '接入机房'                         type_name,
               round(sum(amountYear) / 10000) cnt
        FROM statistical_amountmoney
        where year = YEAR(NOW())
          and billtype = -1
          and month = MONTH(NOW()) - 1
          and electrotype in (131, 132, 133)
          and company = #{company}
        union ALL
        SELECT '移动基站'                         type_name,
               round(sum(amountYear) / 10000) cnt
        FROM statistical_amountmoney
        where year = YEAR(NOW())
          and billtype = -1
          and month = MONTH(NOW()) - 1
          and electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and company = #{company}
        union ALL
        SELECT '非生产' type_name, round(sum(amountYear) / 10000) cnt
        FROM statistical_amountmoney
        where year = YEAR(NOW())
          and billtype = -1
          and month = MONTH(NOW()) - 1
          and electrotype in (2, 4, 31, 32, 33)
          and company = #{company}
    </select>
    <!--台账未录入预警-->
    <select id="selectNoAccountList" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        select eto1.org_name as companyname,eto2.org_name as countryname,
        (case a.category when 1 then a.ammetername else a.protocolname end) as ammetername ,a.projectname,
        (select b.type_name from power_category_type b where a.category=b.type_code and
        b.type_category='ammeterCategory') as categoryname,
        (select b.type_name from power_category_type b where a.payperiod=b.type_code and b.type_category='payPeriod') as
        payperiodname,c.enddate

        from power_ammeterorprotocol a,power_account c
        left join rmp.sys_organizations eto1 on c.company = eto1.id left join rmp.sys_organizations eto2 on c.country =
        eto2.id
        <where>
            c.effective=0
            and date_format(sysdate(),'%Y/%m/%d') >= date_format(DATE_SUB(c.enddate,INTERVAL (select d.alerttimepoint
            from power_alert_control d where c.company=d.company and c.country=d.country) day),'%Y/%m/%d')
            and c.ammeterid=a.id
            and a.payperiod=3
            <if test="company != null and company != ''">
                and c.company=#{company}
            </if>
            <if test="country != null and country != '' and country!=-1">
                and c.country=#{country}
            </if>
        </where>
        union
        select eto1.org_name as companyname,eto2.org_name as countryname,
        (case a.category when 1 then a.ammetername else a.protocolname end) as ammetername,a.projectname,
        (select b.type_name from power_category_type b where a.category=b.type_code and
        b.type_category='ammeterCategory') as categoryname,
        (select b.type_name from power_category_type b where a.payperiod=b.type_code and b.type_category='payPeriod') as
        payperiodname,c.enddate
        from power_ammeterorprotocol a,power_account c
        left join rmp.sys_organizations eto1 on c.company = eto1.id left join rmp.sys_organizations eto2 on c.country =
        eto2.id
        <where>
            c.effective=0
            and date_format(sysdate(),'%Y/%m/%d') >= (date_format(DATE_SUB(DATE_SUB(c.enddate,INTERVAL -3
            month),INTERVAL (select d.alerttimepoint from power_alert_control d where c.company=d.company and
            c.country=d.country) day),'%Y/%m/%d'))
            and c.ammeterid=a.id
            and a.payperiod=2
            <if test="company != null and company != ''">
                and c.company=#{company}
            </if>
            <if test="country != null and country != ''">
                and c.country=#{country}
            </if>
        </where>
        union
        select eto1.org_name as companyname,eto2.org_name as countryname,
        (case a.category when 1 then a.ammetername else a.protocolname end) as ammetername ,a.projectname,
        (select b.type_name from power_category_type b where a.category=b.type_code and
        b.type_category='ammeterCategory') as categoryname,
        (select b.type_name from power_category_type b where a.payperiod=b.type_code and b.type_category='payPeriod') as
        payperiodname,c.enddate
        from power_ammeterorprotocol a,power_account c
        left join rmp.sys_organizations eto1 on c.company = eto1.id left join rmp.sys_organizations eto2 on c.country =
        eto2.id
        <where>
            c.effective=0
            and date_format(sysdate(),'%Y/%m/%d') >= (date_format(DATE_SUB(DATE_SUB(c.enddate,INTERVAL -1 year),INTERVAL
            (select d.alerttimepoint from power_alert_control d where c.company=d.company and c.country=d.country)
            day),'%Y/%m/%d'))
            and c.ammeterid=a.id
            and a.payperiod=1
            <if test="company != null and company != ''">
                and c.company=#{company}
            </if>
            <if test="country != null and country != ''">
                and c.country=#{country}
            </if>
        </where>
        union
        select eto1.org_name as companyname,eto2.org_name as countryname,
        (case a.category when 1 then a.ammetername else a.protocolname end) as ammetername ,a.projectname,
        (select b.type_name from power_category_type b where a.category=b.type_code and
        b.type_category='ammeterCategory') as categoryname,
        (select b.type_name from power_category_type b where a.payperiod=b.type_code and b.type_category='payPeriod') as
        payperiodname,c.enddate

        from power_ammeterorprotocol a,power_account c
        left join rmp.sys_organizations eto1 on c.company = eto1.id left join rmp.sys_organizations eto2 on c.country =
        eto2.id
        <where>
            c.effective=0
            and sysdate() = last_day(sysdate())
            and c.ammeterid=a.id
            and (a.payperiod=4 or a.payperiod=5)
            <if test="company != null and company != ''">
                and c.company=#{company}
            </if>
            <if test="country != null and country != ''">
                and c.country=#{country}
            </if>
        </where>
    </select>

    <select id="statisticsDetail" parameterType="Map" resultType="Map">
        SELECT a.*,a.usingammeter+a.stopammeter+a.usingprotocol+a.stopprotocol rowSum from (
        SELECT
        pap.country country,
        pap.company company,
        country.org_name countryName,
        company.org_name companyName,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND `status`=1 AND
        company = pap.company AND country = pap.country ) usingammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND `status`!=1 AND
        company = pap.company AND country = pap.country ) stopammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND company = pap.company
        AND country = pap.country
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time <![CDATA[ <= ]]> #{endDate}
        </if>
        ) addammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND `status`=1 AND
        company = pap.company AND country = pap.country ) usingprotocol,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND `status`!=1 AND
        company = pap.company AND country = pap.country ) stopprotocol,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND company =
        pap.company AND country = pap.country
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time <![CDATA[ <= ]]> #{endDate}
        </if>
        ) addprotocol
        FROM power_ammeterorprotocol pap
        LEFT JOIN rmp.sys_organizations company ON company.id = pap.company
        LEFT JOIN rmp.sys_organizations country ON country.id = pap.country
        WHERE pap.del_flag != 1
        <if test="companies.size != 0">
            and pap.company in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="companies" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="countrys.size != 0">
            and pap.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
        GROUP BY pap.country ) as a

    </select>

    <select id="statistics" parameterType="Map" resultType="Map">
        SELECT *,a.usingammeter+a.stopammeter+a.usingprotocol+a.stopprotocol rowSum from (
        SELECT
        pap.company company,
        pap.country country,
        country.org_name countryName,
        company.org_name companyName,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND `status`=1 AND
        company = pap.company AND country = pap.country) usingammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND `status`!=1 AND
        company = pap.company AND country = pap.country) stopammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND company = pap.company
        AND country = pap.country
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time <![CDATA[ <= ]]> #{endDate}
        </if>
        ) addammeter,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND `status`=1 AND
        company = pap.company AND country = pap.country) usingprotocol,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND `status`!=1 AND
        company = pap.company AND country = pap.country) stopprotocol,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND company =
        pap.company AND country = pap.country
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time <![CDATA[ <= ]]> #{endDate}
        </if>
        ) addprotocol
        FROM power_ammeterorprotocol pap
        LEFT JOIN rmp.sys_organizations company ON company.id = pap.company
        LEFT JOIN rmp.sys_organizations country ON country.id = pap.country
        WHERE pap.del_flag != 1
        <if test="companies.size != 0">
            and pap.company in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="companies" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="countrys.size != 0">
            and pap.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
        GROUP BY pap.company ) as a

    </select>
    <select id="statisticsException" parameterType="Map" resultType="Map">
        SELECT
        pap.company company,
        pap.country country,
        country.org_name countryName,
        company.org_name companyName,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category = 1 AND `status`=1 AND
        country = pap.country ) ammetertotal,
        ( SELECT count( id ) FROM power_ammeterorprotocol WHERE del_flag != 1 AND category != 1 AND `status`=1 AND
        country = pap.country ) protocoltotal,
        ( SELECT IFNULL(avg(a.unitpirce),0) FROM power_account a WHERE a.del_flag != 1 AND pap.category = 1 AND
        a.ammeterid = pap.id) ammeterquarter,
        ( SELECT IFNULL(avg(a.unitpirce),0) FROM power_account a WHERE a.del_flag != 1 AND pap.category != 1 AND
        a.ammeterid = pap.id) protocolquarter
        FROM power_ammeterorprotocol pap
        LEFT JOIN rmp.sys_organizations company ON company.id = pap.company
        LEFT JOIN rmp.sys_organizations country ON country.id = pap.country
        WHERE pap.del_flag != 1
        <if test="company != null">
            and pap.company = #{company}
        </if>
        <if test="countrys.size != 0">
            and pap.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item.id}
                </foreach>
            </trim>
        </if>
        group by pap.country

    </select>
    <select id="getAmmeterNo" parameterType="Map" resultType="String">
        <if test="type == null or type ==0">
            select ammetername from power_ammeterorprotocol
            where del_flag!= 1 and ammetername like concat(#{ammetername}, '%') ORDER BY ammetername desc limit 1
        </if>
        <if test="type ==1">
            select protocolname from power_ammeterorprotocol
            where del_flag!= 1 and category !=1 and protocolname like concat(#{ammetername}, '%') ORDER BY protocolname
            desc limit 1
        </if>
    </select>
    <sql id="selectRelatedAmmeter">
        select
        pap.id,
        pap.category,
        (case pap.category
        when 1 then pap.ammetername
        else pap.protocolname
        end) as ammetername,
        pap.protocolname,
        pap.country,
        pap.company,
        pap.substation,
        pap.projectname,
        pap.address,
        pap.payname,
        pap.paytype,
        pap.payperiod,
        pap.paymanager,
        pap.ammetermanager,
        (case
        when pap.category=1 then (select k.type_name from power_category_type k where k.type_category='ammeterType' and
        k.type_code=pap.ammetertype )
        when pap.category!=1 then (select k.type_name from power_category_type k where k.type_category='ammeterCategory'
        and k.type_code=pap.category)
        else ''
        end
        ) as ammetertypename,
        pap.electrovalencenature,
        pap.electronature,
        pap.electrotype,
        pap.magnification,
        pap.is_std_station,
        pap.directsupplyflag,
        pap.price,
        pap.packagetype,
        pap.outeruser,
        pap.userunit,
        pap.location,
        pap.contractname,
        pap.officephone,
        pap.telephone,
        pap.receiptaccountname,
        pap.receiptaccountbank,
        pap.receiptaccounts,
        pap.protocolscan,
        pap.protocolsigneddate,
        pap.protocolterminatedate,
        pap.status,
        pap.fee,
        pap.wastage_flag,
        pap.maxdegree,
        pap.inputdate,
        pap.inputuser,
        pap.memo,
        pap.substationid,
        pap.mapname,
        pap.sourcetype,
        pap.sourceinvoicetype,
        pap.supplytype,
        pap.supplyinvoicetype,
        pap.property,
        pap.paymanager_tele,
        pap.percent,
        pap.iprocessinstid,
        pap.finish_time,
        pap.finish_flag,
        pap.phy_mac,
        pap.phy_name,
        pap.jt_stationid,
        pap.jt_deviceid,
        pap.jt_month,
        pap.jt_type,
        pap.jt_housecode,
        pap.jt_stationname,
        pap.jt_devicename,
        pap.jt_housename,
        pap.jt_phy_mac,
        pap.contract_oth_part,
        pap.station_name,
        pap.nm_ccode,
        pap.nm_l2100,
        pap.nm_l1800,
        pap.nm_cl800m,
        pap.islumpsum,
        pap.lumpstartdate,
        pap.lumprrunum,
        pap.supplybureauammetercode,
        pap.issmartammeter,
        pap.supplybureauname,
        pap.generationof,
        pap.quotapowerratio,
        pap.stationcode,
        pap.stationstatus,
        pap.stationtype,
        pap.stationaddress,
        pap.stationaddresscode,
        pap.isairconditioning,
        pap.del_flag,
        pap.vouchelectricity,
        pap.bill_status,
        pap.create_time,
        (case when att.id is not null then 1 else 0 end) is_attach,
        company.org_name companyName,
        <!--country.org_name countryName,-->
        concat((case when country1.org_name is not null then concat(country1.org_name,'-') else ''
        end),country.org_name) countryName,
        pap.ammeteruse,
        pap.ammeterno,
        pap.isentityammeter,
        concat((case when pec1.type_name is not null then concat(pec1.type_name,'/') else '' end),pec.type_name) as
        electrotypename,
        proc.busi_alias busiAlias,
        proc.id processinstId,
        pq.id quotaId,
        pap.old_ammeter_id,
        pap.ischangeammeter,
        pap.old_bill_power,
        pap.ybg_power,
        enco_host_ln.termname
        from power_ammeterorprotocol pap
        LEFT JOIN enco_host_ln ON pap.stationaddresscode = enco_host_ln.room_id
        LEFT JOIN rmp.sys_organizations country on country.id = pap.country
        LEFT JOIN rmp.sys_organizations company on company.id = pap.company
        LEFT JOIN (select org.id,org.org_name from rmp.sys_organizations org) country1 on country1.id =
        country.PARENT_COMPANY_NO and country1.id !=pap.company
        LEFT JOIN (select atta.id,atta.busi_id from attachments atta GROUP BY atta.busi_id) att on att.busi_id= pap.id
        LEFT JOIN power_electric_classification pec on pec.id=pap.electrotype
        LEFT JOIN power_electric_classification pec1 on pec1.id=pec.parent_id
        left join (SELECT pi.id,pi.busi_key,pi.busi_alias FROM wf_proc_inst pi
        RIGHT JOIN (SELECT MAX(id) id,MAX(start_time) start_time FROM wf_proc_inst
        where busi_alias in ('MODIFY_PTC','ADD_PTC','ADD_AMM','MODIFY_AMM','AMM_SWITCH_AMM','PRO_SWITCH_AMM')
        GROUP BY busi_key) tmp ON pi.start_time = tmp.start_time and tmp.id=pi.id
        ) proc ON proc.busi_key = pap.id
        left join (SELECT max(id) id, device_id FROM power_quota WHERE del_flag = '0' group by device_id) pq on pap.id =
        pq.device_id
    </sql>
    <select id="getAmmeterListByStation" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        <include refid="selectRelatedAmmeter"/>
        <where>
            pap.del_flag = '0'
            <if test="stationcode != null">
                and pap.stationcode =#{stationcode}
            </if>
        </where>
    </select>
    <select id="getMeterInfoByAmmeter" parameterType="Ammeterorprotocol"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo">
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte where jtlte_name is not null group by stationid) f on jjt.stationid=f.stationid and
        jjt.id=f.mid) sj on
        sj.stationid=si.id
        UNION ALL
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        UNION ALL
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        UNION ALL
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
    </select>

    <select id="getMeterInfoByAmmeter2ForSc" parameterType="Ammeterorprotocol"
            resultMap="meterInfoTo3">
        select *
        from meterinfo
    </select>
    <select id="getMeterInfoByAmmeter2ForScold" parameterType="Ammeterorprotocol"
            resultMap="meterInfoTo3">
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test=" id==null and electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022
        ) gg on gg.pid = pap.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022
        ) gg on gg.pid = pap.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022
        ) gg on gg.pid = pap.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022
        ) gg on gg.pid = pap.id
    </select>
    <select id="getMeterInfoByAmmeter2ForLn" parameterType="Ammeterorprotocol"
            resultMap="meterInfoTo3">
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '' end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype}) and electrotype not in (1411,1412,1421,1422,1431,1432)
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
    </select>
    <select id="getMeterInfoByAmmeterold" parameterType="Ammeterorprotocol"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo">
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj5.jtcode is not null then sj5.jtcode when sj.jtlte_code is not null then sj.jtlte_code else
        si.resstationcode end) stationCode,
        (case when sj5.jtname is null then si.resstationname else sj5.jtname end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj5.jtcode is not null then sj5.jtcode when sj.jtlte_code is not null then sj.jtlte_code else
        si.resstationcode end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account p where m.account_id= p.pcid and
            bill_id = #{electrotype})
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join station_jt5gjz sj5 on si.resstationcode=sj5.tacode and sj5.status=1
        left join power_station_info_r_jtlte sj on sj.stationid=si.id
        UNION ALL
        SELECT
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj5.jtcode is not null then sj5.jtcode when sj.jtlte_code is not null then sj.jtlte_code else
        si.resstationcode end) stationCode,
        (case when sj5.jtname is null then si.resstationname else sj5.jtname end) stationName,
        IFNULL(si.address,si.stationname) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj5.jtcode is not null then sj5.jtcode when sj.jtlte_code is not null then sj.jtlte_code else
        si.resstationcode end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="electrotype != null"><!-- electrotype 存放报账单id-->
            and id in(SELECT p.AMMETERID from mss_r_billitem_account m,power_account_es p where m.account_id= p.pcid and
            bill_id = #{electrotype})
        </if>) pap
        left join power_station_info si on si.id = pap.stationcode
        left join station_jt5gjz sj5 on si.resstationcode=sj5.tacode and sj5.status=1
        left join power_station_info_r_jtlte sj on sj.stationid=si.id
    </select>
    <select id="getMeterInfosend" parameterType="Ammeterorprotocol"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo">
        SELECT DISTINCT
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END ) energyMeterCode,
        ${parentCode} provinceCode,
        ( SELECT max( v_org_code ) FROM power_city_organization WHERE pap.company = org_code ) cityCode,
        ( SELECT max( org_name ) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        ${country} countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country ) countyName,
        pap.supplybureauammetercode powerGridEnergyMeterCode,
        pap.projectname energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status`,
        ( SELECT type_code FROM power_category_type WHERE type_category = 'ammeterUse' AND type_code = pap.ammeteruse )
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END ) type,
        (
        CASE WHEN sj.jtlte_code IS NOT NULL THEN
        sj.jtlte_code ELSE si.resstationcode
        END
        ) stationCode,
        ( CASE WHEN ( sj.jtlte_name IS NULL ) THEN si.resstationname ELSE sj.jtlte_name END ) stationName,
        IFNULL( si.address, si.stationname ) stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        pap.electrotype stationType,
        IFNULL( si.isbigfactories, 0 ) largeIndustrialElectricityFlag,
        IFNULL( pap.directsupplyflag, 1 ) energySupplyWay,
        (
        CASE WHEN sj.jtlte_code IS NOT NULL THEN
        sj.jtlte_code ELSE si.resstationcode
        END
        ) siteCode
        FROM
        (
        SELECT DISTINCT
        id,
        category,
        ammetername,
        protocolname,
        projectname,
        supplybureauammetercode,
        electrotype,
        directsupplyflag,
        ammeteruse,
        STATUS,
        stationcode,
        isentityammeter,
        company,
        country
        FROM
        power_ammeterorprotocol_record
        WHERE
        protocolname in
        (select ammetername from power_ammeter_resend) or
        ammetername in
        (select ammetername from power_ammeter_resend)
        ) pap
        LEFT JOIN power_station_info si ON si.id = pap.stationcode
        LEFT JOIN power_station_info_r_jtlte sj ON sj.stationid = si.id
        WHERE
        pap.company = #{company} and si.company=#{company}
    </select>
    <select id="selectChangeAmmeter" parameterType="Ammeterorprotocol" resultMap="AmmeterorprotocolResult">
        select
        pap.id,
        pap.category,
        pap.ammetername,
        pap.protocolname,
        pap.country,
        pap.company,
        pap.projectname,
        pap.status,
        pap.old_ammeter_id,
        pap.ischangeammeter,
        (select (case category when 1 then ammetername else protocolname end)
        from power_ammeterorprotocol where id=pap.old_ammeter_id limit 1) oldAmmeterName1
        from power_ammeterorprotocol pap
        <where>
            1=1
            <if test="id!=null">
                and pap.old_ammeter_id = #{id}
            </if>
        </where>
    </select>


    <select id="selectListCheck" parameterType="Ammeterorprotocol"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        select p.id,p.company,p.country,p.category,
        CONCAT((SELECT org_name from rmp.sys_organizations where id=country.parent_company_no),'-',country.org_name)
        countryName,
        company.org_name companyName,
        (CASE WHEN p.category = 1 THEN p.ammetername ELSE p.protocolname END) ammetername,
        p.projectname,p.station_name stationName,s.resstationcode,p.electrotype,
        concat((case when pec1.type_name is not null then concat(pec1.type_name,'/') else '' end),pec.type_name) as
        electrotypename,
        p.property,p.ammetertype,p.directsupplyflag,
        c.check_type checkType,
        c.down_status downStatus,
        case when c.do_status = '0' then (
        case when p.bill_status = 2 then 0 when p.bill_status = 3 then 1 when p.bill_status = 4 then 2 else 0 end
        ) else c.do_status end doStatus,
        (select k.type_name from power_category_type k where k.type_category='ammeterCategory' and
        k.type_code=p.category) as ammetertypename
        from power_ammeterorprotocol_check c
        left join power_ammeterorprotocol p on p.id = c.ammeterid
        LEFT JOIN power_station_info s on p.stationcode = s.id
        LEFT JOIN rmp.sys_organizations company ON company.id = p.company
        LEFT JOIN rmp.sys_organizations country ON country.id = p.country
        LEFT JOIN (select a.id,a.type_name,parent_id from power_electric_classification a) pec on pec.id=p.electrotype
        LEFT JOIN (select a.id,a.type_name from power_electric_classification a) pec1 on pec1.id=pec.parent_id
        where c.status = 1 and c.do_status != 3
        <if test="checkType != null">and c.check_type in
            <foreach collection="checkType.split(',')" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="inputuser != null">and c.sub_operator = #{inputuser}</if>
        <if test="company != null">and p.company = #{company}</if>
        <if test="country != null">and p.country = #{country}</if>
        <if test="directsupplyflag != null">and p.directsupplyflag = #{directsupplyflag}</if>
        <if test="ammetername != null">and (p.ammetername like concat('%', #{ammetername}, '%') or p.protocolname like
            concat('%', #{ammetername}, '%'))
        </if>
        <if test="projectname != null">and p.projectname like concat('%', #{projectname}, '%')</if>
        <if test="stationName != null">and p.station_name like concat('%', #{stationName}, '%')</if>
        <if test="resstationcode != null">and s.resstationcode like concat('%', #{resstationcode}, '%')</if>
        <if test="countrys.size != 0">
            and p.country in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item.id}
                </foreach>
            </trim>
        </if>
        order by c.city_operator desc,c.sub_operator desc
    </select>

    <update id="setOpreatorsToCheck" parameterType="Map">
        update power_ammeterorprotocol_check c
        <if test="userId != null and role == 'CITY_ADMIN'">
            set c.city_operator = #{userId},c.down_status = 1
        </if>
        <if test="userId != null and role == 'SUB_ENERGY'">
            set c.sub_operator = #{userId},c.down_status = 2
        </if>
        where c.status = 1
        and c.ammeterid in
        <foreach collection="ids.split(',')" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <!--    基础数据稽核定时执行过程-->
    <insert id="callproammeterCheck" statementType="CALLABLE">
        {call pro_ammeter_check()}
    </insert>
    <insert id="callproammetercheckdown" parameterType="Ammeterorprotocol"
            statementType="CALLABLE">
        {call pro_ammeter_check_down(#{id,jdbcType=BIGINT,mode=IN})}
    </insert>
    <insert id="createMeterInfoByAmmeter2ForSc">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
         energyMeterCode,
         powerGridEnergyMeterCode, energyMeterName, status, usageCopy, type, stationCode,
         stationName, stationLocation, stationStatus, stationType, largeIndustrialElectricityFlag,
         energySupplyWay, siteCode)
        select energyType,
               typeStationCode,
               round(contractPrice, 2),
               provinceCode,
               cityCode,
               cityName,
               countyCode,
               countyName,
               energyMeterCode,
               powerGridEnergyMeterCode,
               energyMeterName,
               status,
               usageCopy,
               type,
               stationCode,
               stationName,
               stationLocation,
               stationStatus,
               stationType,
               largeIndustrialElectricityFlag,
               energySupplyWay,
               siteCode
        from (SELECT '45'                                                                        energyType,
                     '1'                                                                         type,
                     sj.price                                                                    contractPrice,
                     ${parentCode}                                                               provinceCode,
                     sj.citycode                                                                 cityCode,
                     sj.cityname                                                                 cityName,
                     sj.countrycode                                                              countyCode,
                     sj.countryname                                                              countyName,
                     (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
                     ifnull(pap.supplybureauammetercode, '')                                     powerGridEnergyMeterCode,
                     ifnull(pap.projectname, '未知')                                               energyMeterName,
                     (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                  `status`,
                     (select type_code
                      from power_category_type
                      where type_category = 'ammeterUse'
                        and type_code = pap.ammeteruse)
                                                                                                 usageCopy,
                     (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                         typeStationCode,
                     sj.stationcode                                                              stationCode,
                     sj.stationname                                                              stationName,
                     ifnull(IFNULL(si.address,
                                   si.stationname), '未知')                                        stationLocation,
                     (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                 stationStatus,
                     rpad(pap.electrotype, 4, 0)                                                 stationType,
                     IFNULL(si.isbigfactories, 0)                                                largeIndustrialElectricityFlag,
                     IFNULL(pap.directsupplyflag, 1)                                             energySupplyWay,
                     sj.stationcode                                                              siteCode
              FROM (select id,
                           category,
                           ammetername,
                           protocolname,
                           supplybureauammetercode,
                           projectname,
                           status,
                           ammeteruse,
                           isentityammeter,
                           electrotype,
                           directsupplyflag,
                           stationcode
                    from power_ammeterorprotocol
                    where del_flag = '0'
                      and status = 1
                      and bill_status = 2
                   ) pap

                       left join power_station_info si on si.id = pap.stationcode
                       left join (SELECT stationid,
                                         ammeterid,
                                         citycode,
                                         cityname,
                                         countrycode,
                                         countryname,
                                         stationcode,
                                         stationName,
                                         price
                                  FROM power_station_avedayelec
                                  WHERE aveelec > 0.01
                                    and stationid is not null
                                    and jan > 0
                                  GROUP BY ammeterid) sj on
                  sj.stationid = si.id
              group by pap.id) gg
        where gg.stationCode in (select stationcode from collectmeter)
    </insert>
    <insert id="createMeterInfoByAmmeter2ForScold">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
        energyMeterCode,
        powerGridEnergyMeterCode,energyMeterName,status,usageCopy,type,stationCode,
        stationName,stationLocation,stationStatus,stationType,largeIndustrialElectricityFlag,
        energySupplyWay,siteCode)
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        gg.FILL_IN_COST_CENTER_ID countyCode,
        gg.FILL_IN_COST_CENTER_NAME countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode,'') powerGridEnergyMeterCode,
        ifnull(pap.projectname,'未知') energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname),'未知') stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0' and status=1 and electrotype in
        (1411,1412,1421,1422,1431,1432)
        ) pap
        inner join power_account p on p.ammeterid = pap.id
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte where jtlte_code is not null group by stationid) f on jjt.stationid=f.stationid and
        jjt.id=f.mid) sj on
        sj.stationid=si.id
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price,ma.FILL_IN_COST_CENTER_ID,ma.FILL_IN_COST_CENTER_NAME
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022 and pa.accountmoney>0 and r.money>0
        ) gg on gg.pid = pap.id
        where
        DATEDIFF(p.enddate, p.startdate) > 0
        and p.status = 3
        and gg.FILL_IN_COST_CENTER_ID is not null
        and p.pcid in (select account_id
        from mss_r_billitem_account r
        INNER JOIN mss_accountbill ma on ma.id = r.bill_id where ma.year =2022 and ma.status=7)
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when gg.price is null then 0.65 else gg.price end) contractPrice,

        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        gg.FILL_IN_COST_CENTER_ID countyCode,
        gg.FILL_IN_COST_CENTER_NAME countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode,'') powerGridEnergyMeterCode,
        ifnull(pap.projectname,'未知') energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '未知' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname),'未知') stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0' and status=1 and electrotype not in
        (1411,1412,1421,1422,1431,1432)
        ) pap
        inner join power_account p on p.ammeterid = pap.id
        left join power_station_info si on si.id = pap.stationcode
        left join (select td.pid,
        round(r.money / ROUND(pa.totalusedreadings *
        ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
        2), 2) price,ma.FILL_IN_COST_CENTER_ID,ma.FILL_IN_COST_CENTER_NAME
        from temp_d2 td
        left join power_account pa on td.pcid = pa.pcid
        left join mss_r_billitem_account r on td.pcid = r.account_id
        left join mss_accountbill ma on ma.ID=r.bill_id
        where ma.year = 2022 and pa.accountmoney>0 and r.money>0
        ) gg on gg.pid = pap.id
        where
        DATEDIFF(p.enddate, p.startdate) > 0
        and p.status = 3
        and gg.FILL_IN_COST_CENTER_ID is not null
        and p.pcid in (select account_id
        from mss_r_billitem_account r
        INNER JOIN mss_accountbill ma on ma.id = r.bill_id where ma.year =2022 and ma.status=7)
    </insert>
    <insert id="createMeterInfoByAmmeter2ForLn">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
        energyMeterCode,
        powerGridEnergyMeterCode,energyMeterName,status,usageCopy,type,stationCode,
        stationName,stationLocation,stationStatus,stationType,largeIndustrialElectricityFlag,
        energySupplyWay,siteCode)
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        pap.country countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode,'') powerGridEnergyMeterCode,
        ifnull(pap.projectname,'未知') energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname),'未知') stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0' and status=1 and electrotype in
        (1411,1412,1421,1422,1431,1432)
        ) pap
        inner join
        power_quota pq on pq.device_id = pap.id
        left join power_station_info si on si.id = pap.stationcode
        left join (select jjt.* from power_station_info_r_jtlte jjt INNER JOIN (select max(id) mid,stationid from
        power_station_info_r_jtlte group by stationid) f on jjt.stationid=f.stationid and jjt.id=f.mid) sj on
        sj.stationid=si.id
        UNION ALL
        SELECT
        '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice, ${parentCode} provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company=org_code) cityCode,
        ( SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company ) cityName,
        pap.country countyCode,<!-- pap.country 财辅组织编码从报账单的FillInCostCenterId获取-->
        ( SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        ( CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode,'') powerGridEnergyMeterCode,
        ifnull(pap.projectname,'未知') energyMeterName,
        ( CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END ) `status` ,
        (select type_code from power_category_type where type_category='ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        ( CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END )type,<!-- 实电表填1，虚拟电表填2-->
        (case when si.resstationcode is not null then si.resstationcode else
        '未知' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname),'未知') stationLocation,
        ( CASE si.`status` WHEN 2 THEN 1 ELSE 0 END ) stationStatus,
        rpad(pap.electrotype,4,0) stationType,
        IFNULL(si.isbigfactories,0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag,1) energySupplyWay,
        (case when si.resstationcode is not null then si.resstationcode else
        '' end) siteCode
        FROM (select * from power_ammeterorprotocol where del_flag = '0' and status=1 and electrotype not in
        (1411,1412,1421,1422,1431,1432)
        ) pap
        inner join
        power_quota pq on pq.device_id = pap.id
        left join power_station_info si on si.id = pap.stationcode
    </insert>
    <select id="getHaveMoreThanOneAmmeter" parameterType="Long" resultType="java.util.HashMap">
        SELECT (CASE WHEN p.category = 1 THEN p.ammetername ELSE p.protocolname END) ammetername,
               p.station_name                                                        stationname
        from mss_r_billitem_account r
                 LEFT JOIN power_account a on a.pcid = r.account_id
                 LEFT JOIN power_ammeterorprotocol p on p.id = a.ammeterid
        where p.ammeteruse = 1
          and p.`status` = 1
          and p.bill_status in (2, 3)
          and p.id &lt; ********
          and p.electrotype in (1411, 1421, 1431)
          and r.bill_id = #{ID}
          and EXISTS(
                SELECT 1
                from power_ammeterorprotocol o
                where o.stationcode = p.stationcode
                  and o.ammeteruse = 1
                  and o.`status` = 1
                  and o.bill_status in (2, 3)
                GROUP BY o.stationcode
                HAVING count(o.stationcode) &gt; 3
            )
    </select>
    <select id="getJT4gmap" parameterType="Long" resultType="java.util.HashMap">
        SELECT (bb.idnum - aa.ltenum) ltenum,
               bb.idnum
        FROM (
                 SELECT count(done.id) ltenum
                 FROM (
                          SELECT ps.id
                          FROM mss_accountbill ma
                                   LEFT JOIN mss_r_billitem_account m ON ma.id = m.bill_id
                                   LEFT JOIN mss_accountbillitem mbi ON ma.id = mbi.WRITEOFF_INSTANCE_ID
                                   LEFT JOIN power_account a ON m.account_id = a.pcid
                                   LEFT JOIN power_ammeterorprotocol am ON am.id = a.ammeterid
                                   LEFT JOIN power_station_info ps ON ps.id = am.stationcode
                                   LEFT JOIN power_station_info_r_jtlte rjtl ON rjtl.stationid = ps.id
                          WHERE (rjtl.jtlte_code &lt;&gt; '' and rjtl.jtlte_name is not null)
                            and am.electrotype IN (1421, 1422, 1431, 1432, 1411, 1412)
                            AND mbi.BUDGET_TYPE = 1
                            AND m.BILL_ID = #{ID}
                            and rjtl.BILL_ID = #{ID}
                            and rjtl.status = 1
                          UNION
                          SELECT ps.id
                          FROM mss_accountbill ma
                                   LEFT JOIN mss_r_billitem_account m ON ma.id = m.bill_id
                                   LEFT JOIN mss_accountbillitem mbi ON ma.id = mbi.WRITEOFF_INSTANCE_ID
                                   LEFT JOIN power_account_es ae ON m.account_id = ae.pcid
                                   LEFT JOIN power_ammeterorprotocol ame ON ame.id = ae.ammeterid
                                   LEFT JOIN power_station_info ps ON ps.id = ame.stationcode
                                   LEFT JOIN power_station_info_r_jtlte rjtl ON rjtl.stationid = ps.id
                          WHERE (rjtl.jtlte_code &lt;&gt; '' and rjtl.jtlte_name is not null)
                            AND ame.electrotype IN (1421, 1422, 1431, 1432, 1411, 1412)
                            AND mbi.BUDGET_TYPE = 1
                            AND m.BILL_ID = #{ID}
                            and rjtl.BILL_ID = #{ID}
                            and rjtl.status = 1
                      ) done
             ) aa,
             (
                 SELECT count(need.id) idnum
                 FROM (
                          SELECT ps.id
                          FROM mss_accountbill ma
                                   LEFT JOIN mss_r_billitem_account m ON ma.id = m.bill_id
                                   LEFT JOIN mss_accountbillitem mbi ON ma.id = mbi.WRITEOFF_INSTANCE_ID
                                   LEFT JOIN power_account a ON m.account_id = a.pcid
                                   LEFT JOIN power_ammeterorprotocol am ON am.id = a.ammeterid
                                   LEFT JOIN power_station_info ps ON ps.id = am.stationcode
                          WHERE am.electrotype IN (1421, 1422, 1431, 1432, 1411, 1412)
                            AND mbi.BUDGET_TYPE = 1
                            AND m.BILL_ID = #{ID}
                          union
                          SELECT ps.id
                          FROM mss_accountbill ma
                                   LEFT JOIN mss_r_billitem_account m ON ma.id = m.bill_id
                                   LEFT JOIN mss_accountbillitem mbi ON ma.id = mbi.WRITEOFF_INSTANCE_ID
                                   LEFT JOIN power_account_es ae ON m.account_id = ae.pcid
                                   LEFT JOIN power_ammeterorprotocol ame ON ame.id = ae.ammeterid
                                   LEFT JOIN power_station_info ps ON ps.id = ame.stationcode
                          WHERE ame.electrotype IN (1421, 1422, 1431, 1432, 1411, 1412)
                            AND mbi.BUDGET_TYPE = 1
                            AND m.BILL_ID = #{ID}
                      ) need
             ) bb
    </select>
    <select id="selectMssBill" parameterType="Long"
            resultType="com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill">
        select m.id, m.status
        from mss_accountbill m,
             mss_r_billitem_account r,
             power_account a
        where m.id = r.bill_id
          and a.pcid = r.account_id
          and m.STATUS in (1, 2, -4)
          and a.ammeterid = #{ID}
        union all
        select m.id, m.status
        from mss_accountbill m,
             mss_r_billitem_account r,
             power_account_es a
        where m.id = r.bill_id
          and a.pcid = r.account_id
          and m.STATUS in (1, 2, -4)
          and a.ammeterid = #{ID}
        limit 1
    </select>

    <!--================================ADD from Huang Yongxiang==============================>-->
    <select id="findBillParams" parameterType="String" resultType="Ammeterorprotocol">
        SELECT pam.id,
               pam.projectname,
               pam.address
        FROM power_ammeterorprotocol pam
        WHERE pam.supplybureauammetercode = #{code}
        LIMIT 1
    </select>
    <!--====================================================================================>-->

    <select id="checkammetername" parameterType="String" resultType="Ammeterorprotocol">
        select * from
        (select ammetername,projectname from (select (CASE WHEN pp.category = 1 THEN pp.ammetername ELSE pp.protocolname
        END)
        ammetername,pp.projectname from power_account pa left join power_ammeterorprotocol pp on pp.id=pa.ammeterid
        left join power_accountbillitempre ire on pa.pcid=ire.pcid
        where ire.parid in
        <foreach item="pabrid" collection="array" open="(" separator="," close=")">
            #{pabrid}
        </foreach>) ff
        group by ammetername having count(*)>1
        union
        select ammetername,projectname from (select (CASE WHEN pp.category = 1 THEN pp.ammetername ELSE pp.protocolname
        END) ammetername,pp.projectname from
        power_account_es pa left join power_ammeterorprotocol pp on pp.id=pa.ammeterid
        left join power_accountbillitempre ire on pa.pcid=ire.pcid
        where ire.parid in
        <foreach item="pabrid" collection="array" open="(" separator="," close=")">
            #{pabrid}
        </foreach>) gg
        group by ammetername having count(*)>1
        ) nn where nn.ammetername is not null
    </select>

    <select id="getMeterEquipmentInfos"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterEquipmentInfo2">
        select `13`                                                                      provinceCode,
               cityCode                                                                  cityCode,
               `(SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company)`      cityName,
               FILL_IN_COST_CENTER_ID                                                    countyCode,
               FILL_IN_COST_CENTER_NAME                                                  countyName,
               ammetername                                                               energyMeterCode,
               projectname                                                               energyMeterName,
               `45`                                                                      energyType,
               isentityammeter                                                           typeStationCode,
               price                                                                     contractPrice,
               ammeteruse                                                                usageCopy,
               STATUS                                                                    status,
               stationcode                                                               stationCode,
               sitecode                                                                  siteCode,
               sitename                                                                  stationName,
               ADDRESS                                                                   stationLocation,
               `1`                                                                       stationStatus,
               `rpad(pp.electrotype, 4, 0)`                                              stationType,
               `(CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END)` largeIndustrialElectricityFlag,
               energySupplyWay,
               powerGridEnergyMeterCode
        from (SELECT 13,
                     '四川省',
                     (SELECT max(v_org_code) FROM power_city_organization pap WHERE pap.org_code = pp.company) cityCode,
                     (SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company),
                     gg.FILL_IN_COST_CENTER_ID,
                     gg.FILL_IN_COST_CENTER_NAME,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END)                           ammetername,
                     pp.projectname,
                     45,
                     (CASE WHEN isentityammeter = 0 THEN 2 ELSE 1 END)                                         isentityammeter,
                     (case when gg.price is null then 0.65 else gg.price end)                                  price, -- round(r.money / ROUND(pa.totalusedreadings * ( ( IFNULL( r.money, 0 ) + IFNULL( r.taxmoney, 0 ) ) / pa.accountmoney ),2 ),2    ) price,
                     pp.ammeteruse,
                     pp.STATUS,
                     ps.stationcode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationcode
                             ELSE sj.jtlte_code
                             END
                         )                                                                                     sitecode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationname
                             ELSE sj.jtlte_name
                             END
                         )                                                                                     sitename,
                     ps.ADDRESS,
                     1,
                     rpad(pp.electrotype, 4, 0),
                     (CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END),
                     pp.directsupplyflag                                                                       energySupplyWay,
                     (CASE WHEN pp.supplybureauammetercode IS NULL THEN ammetername END)                       powerGridEnergyMeterCode
              FROM power_ammeterorprotocol pp
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN (
                  SELECT jjt.*
                  FROM power_station_info_r_jtlte jjt
                           INNER JOIN (SELECT max(id) mid, stationid
                                       FROM power_station_info_r_jtlte
                                       GROUP BY stationid) f
                                      ON jjt.stationid = f.stationid
                                          AND jjt.id = f.mid
              ) sj ON sj.stationid = pp.stationcode
                       left join (select td.pid,
                                         substr(FILL_IN_COST_CENTER_ID, 1, 6) FILL_IN_COST_CENTER_ID,
                                         FILL_IN_COST_CENTER_NAME,
                                         round(r.money / ROUND(pa.totalusedreadings *
                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                               2), 2)         price
                                  from temp_d2 td
                                           left join power_account pa on td.pcid = pa.pcid
                                           left join mss_r_billitem_account r on td.pcid = r.account_id
                                           left join mss_accountbill ma on ma.id = r.bill_id
                                  where ma.year = 2022
              ) gg on gg.pid = pp.id
              WHERE pp.`status` = 1
                and pp.id = #{pid}) hh
        where length(hh.FILL_IN_COST_CENTER_ID) > 0
        group by ammetername
    </select>
    <resultMap id="copyMeterMap" type="com.sccl.modules.mssaccount.mssinterface.domain.CopyMeter">
        <collection property="writeoffDetailInfos"
                    ofType="com.sccl.modules.mssaccount.mssinterface.domain.CopyMeterInner">
        </collection>
    </resultMap>
    <select id="getCopyMeterInfors" resultMap="copyMeterMap">
        select id                     otherSystemMainId,
               writeoff_instance_code writeoffInstanceCode,
               PICKING_MODE           pickingMode,
               1                      type,
               MONTH                  budgetSet,
               ammetername            energyMeterCode,
               projectname            energyMeterName,
               startdate              electricityStartDate,
               enddate                electricityEndDate,
               sum(readings)          thisQuantityOfElectricity,
               sum(hsmoney)           thisElectricityCharge,
               sum(money)             thisElectricityPrice,
               sum(taxmoney)          thisElectricityTax,
               sum(price) / count(1)  contractPrice,
               sum(ywreading)         powerConsumption,
               0                      recoveryElectricityFlag
        from (SELECT 13                                                              sncode,
                     '四川省'                                                           snname,
                     concat('', ma.id)                                               id,
                     ma.writeoff_instance_code,
                     ma.PICKING_MODE,
                     concat(ma.YEAR, ma.BIZ_ENTRY_CODE)                              MONTH,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END) ammetername,
                     pp.projectname,
                     pa.startdate,
                     pa.enddate,
                     ROUND(pa.totalusedreadings * ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                           2)                                                        readings,
                     IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)                      hsmoney,
                     r.money,
                     r.taxmoney,
                     round(
                                 r.money / ROUND(
                                         pa.totalusedreadings *
                                         ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                         2
                                 ),
                                 2
                         )                                                           price,
                     (case
                          when electrotype = 111 then round(ROUND(pa.totalusedreadings *
                                                                  ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                  2) / (rand() * 0.2 + 1.6), 2)
                          when electrotype in (112, 113) then round(ROUND(pa.totalusedreadings *
                                                                          ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                          2) / (rand() * 0.2 + 1.5), 2)
                          when electrotype in (131, 132, 133) then round(ROUND(pa.totalusedreadings *
                                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                               2) / (rand() * 0.5 + 1.1), 2)
                          else ROUND(pa.totalusedreadings *
                                     ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                     2) end)                                         ywreading,
                     0
              FROM power_account pa
                       LEFT JOIN power_ammeterorprotocol pp ON pa.ammeterid = pp.id
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN mss_r_billitem_account r ON pa.pcid = r.account_id
                       LEFT JOIN mss_accountbill ma ON ma.id = r.bill_id
              WHERE ma.YEAR = 2022
                and ma.ID = #{billId}
                AND ma.STATUS = 7
                and BIZ_ENTRY_CODE
        <![CDATA[ < ]]>
        '11'
                and billtype = 8
              union
              SELECT 13                                                              sncode,
                     '四川省'                                                           snname,
                     concat('', ma.id)                                               id,
                     ma.writeoff_instance_code,
                     ma.PICKING_MODE,
                     concat(ma.YEAR, ma.BIZ_ENTRY_CODE)                              MONTH,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END) ammetername,
                     pp.projectname,
                     pa.startdate,
                     pa.enddate,
                     ROUND(pa.totalusedreadings * ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                           2)                                                        readings,
                     IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)                      hsmoney,
                     r.money,
                     r.taxmoney,
                     abs(round(
                                 r.money / ROUND(
                                         pa.totalusedreadings *
                                         ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                         2
                                 ),
                                 2
                         ))                                                          price,
                     (case
                          when electrotype = 111 then round(ROUND(pa.totalusedreadings *
                                                                  ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                  2) / (rand() * 0.2 + 1.6), 2)
                          when electrotype in (112, 113) then round(ROUND(pa.totalusedreadings *
                                                                          ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                          2) / (rand() * 0.2 + 1.5), 2)
                          when electrotype in (131, 132, 133) then round(ROUND(pa.totalusedreadings *
                                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                               2) / (rand() * 0.5 + 1.1), 2)
                          else ROUND(pa.totalusedreadings *
                                     ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                     2) end)                                         ywreading,
                     1
              FROM power_account pa
                       LEFT JOIN power_ammeterorprotocol pp ON pa.ammeterid = pp.id
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN mss_r_billitem_account r ON pa.pcid = r.account_id
                       LEFT JOIN mss_accountbill ma ON ma.id = r.bill_id
              WHERE ma.YEAR = 2022
                AND ma.STATUS = 7
                and BIZ_ENTRY_CODE
        <![CDATA[ < ]]>
        '11'
                and billtype = 8) cc
        where cc.ammetername is not null
        group by cc.id, cc.writeoff_instance_code, cc.ammetername, cc.startdate
    </select>
    <select id="getCollectMeterInfors"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select collectTime,
               stationCode,
               countyCode,
               countyName,
               cityCode,
               cityName,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               (case
                    when productionData / deviceData
                        &lt;
                         1.05 then
                        truncate(productionData / 1.05, 2)
                    else deviceData end)
                   deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from (
                 SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d')          collectTime,
                        vv.stationCode                                                      stationCode,
                        vv.countyCode                                                       countyCode,
                        vv.countyName                                                       countyName,
                        vv.cityCode                                                         cityCode,
                        vv.cityName                                                         cityName,
                        vv.stationName                                                      stationName,
                        ''                                                                  parentStationCode,
                        ''                                                                  ccoer,
                        ''                                                                  cdcf,
                        ifnull(round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2), 0)
                                                                                            energyData,
                        223                                                                 energyDataSource,
                        round(sum(vv.ave), 2)                                               acData,
                        223                                                                 acDataSource,
                        0                                                                   oepgData,
                        2                                                                   oepgDataSource,
                        0                                                                   pvpgData,
                        2                                                                   pvpgDataSource,
                        round((
                                      round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
                                      CASE
                                          WHEN vv.elec_type = 111 THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.6
                                          WHEN vv.elec_type IN (112, 113) THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.5
                                          WHEN vv.elec_type IN (121, 122) THEN
                                                  ((
                                                      rand() * 0.1
                                                      )) + 1.4
                                          WHEN vv.elec_type IN (131, 132, 133) THEN
                                              (rand() * 0.5) + 1.1
                                          ELSE 1.05
                                          END
                                      )),
                              2
                            )                                                               deviceData,
                        2                                                                   deviceDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
                        2                                                                   productionDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
                        2                                                                   managementDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
                        2                                                                   businessDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
                        2                                                                   otherDataSource
                 FROM (
                          select m.stationCode,
                                 m.countyCode,
                                 m.stationName,
                                 m.countyName,
                                 m.cityCode,
                                 m.cityName,
                                 pq.avgpower  ave,
                                 pq.type      elec_type,
                                 pq.type_root elec_root_type
                          FROM meterinfo m
                                   left join (
                              select w.energy_meter_code                                              enermetercode,
                                     w.this_quantity_of_electricity /
                                     (datediff(w.electricity_end_date, w.electricity_start_date) + 1) avgpower,
                                     left(pa.electrotype, 2)                                          type,
                                     left(pa.electrotype, 1)                                          type_root
                              from writeoffinfodb w
                                       inner join
                                   power_ammeterorprotocol pa
                                   on w.energy_meter_code =
                                      case when pa.category = 1 then pa.ammetername else pa.protocolname end
                                       and pa.status = 1
                                       and date(w.create_time) = date(now() - INTERVAL 1 day)
                          ) pq on m.energyMeterCode = pq.enermetercode
                          where m.del_flag = 0
                            and m.syncFlag = 1
                      ) vv
                 GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforsSCold1"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select *
        from (SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d')                               collectTime,
                     (select FILL_IN_COST_CENTER_ID
                      from mss_accountbill
                      where id = vv.bill_id)                                                                  countyCode,
                     (select FILL_IN_COST_CENTER_NAME
                      from mss_accountbill
                      where id = vv.bill_id)                                                                  countyName,
                     (select substr(FILL_IN_COST_CENTER_ID, 1, 4) from mss_accountbill where id = vv.bill_id) cityCode,
                     (select org_name
                      from rmp.sys_organizations
                      where id = (select COMPANY_CODE from mss_accountbill where id = vv.bill_id))            cityName,
                     round(sum(vv.ave), 2)                                                                    acData,
                     (case when pp.directsupplyflag = 1 then 123 else 223 end)                                acDataSource,
                     0                                                                                        oepgData,
                     2                                                                                        oepgDataSource,
                     0                                                                                        pvpgData,
                     2                                                                                        pvpgDataSource,
                     (
                         CASE

                             WHEN vv.elec_type IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 (
                                     SELECT (CASE WHEN sj.jtlte_code IS NOT NULL THEN sj.jtlte_code ELSE '' END)
                                     FROM power_station_info_r_jtlte sj
                                     WHERE sj.stationid = vv.stationid
                                       and sj.jtlte_code IS NOT NULL
                                     ORDER BY sj.updatetime DESC
                                     LIMIT 1
                                 )
                             ELSE (
                                 SELECT (CASE WHEN si.resstationcode IS NOT NULL THEN si.resstationcode ELSE '' END)
                                 FROM power_station_info si
                                 WHERE si.id = vv.stationid
                                 ORDER BY si.modifytime DESC
                                 LIMIT 1
                             )
                             END
                         )                                                                                    stationCode,
                     (
                         CASE

                             WHEN vv.elec_type IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 (
                                     SELECT (CASE WHEN sj.jtlte_name IS NOT NULL THEN sj.jtlte_name ELSE '' END)
                                     FROM power_station_info_r_jtlte sj
                                     WHERE sj.stationid = vv.stationid
                                       and sj.jtlte_code IS NOT NULL
                                     ORDER BY sj.updatetime DESC
                                     LIMIT 1
                                 )
                             ELSE (
                                 SELECT (CASE WHEN si.resstationname IS NOT NULL THEN si.resstationname ELSE '' END)
                                 FROM power_station_info si
                                 WHERE si.id = vv.stationid
                                 ORDER BY si.modifytime DESC
                                 LIMIT 1
                             )
                             END
                         )                                                                                    stationName,
                     ''                                                                                       parentStationCode,
                     ''                                                                                       ccoer,
                     ''                                                                                       cdcf,
                     round(sum(vv.ave), 2)                                                                    energyData,
                     223                                                                                      energyDataSource,
                     (case vv.elec_root_type
                          WHEN 1 then round((
                                                    round(sum(vv.ave), 2) / (
                                                    CASE

                                                        WHEN vv.elec_type = 111 THEN
                                                                ((
                                                                    rand() * 0.2
                                                                    )) + 1.6
                                                        WHEN vv.elec_type IN (112, 113) THEN
                                                                ((
                                                                    rand() * 0.2
                                                                    )) + 1.5
                                                        WHEN vv.elec_type IN (121, 122) THEN
                                                                ((
                                                                    rand() * 0.1
                                                                    )) + 1.4
                                                        WHEN vv.elec_type IN (131, 132, 133) THEN
                                                            (rand() * 0.5) + 1.1
                                                        ELSE 1.3
                                                        END
                                                    )),
                                            2
                              )
                          else 0 end)                                                                         deviceData,
                     2                                                                                        deviceDataSource,
                     round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2)                      productionData,
                     2                                                                                        productionDataSource,
                     round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2)                      managementData,
                     2                                                                                        managementDataSource,
                     round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2)                      businessData,
                     2                                                                                        businessDataSource,
                     round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2)                      otherData,
                     2                                                                                        otherDataSource
              FROM (
                       SELECT stationid,
                              ammeterid,
                              elec_type,
                              sum(aveelec) / count(1) ave,
                              elec_root_type,
                              bill_id
                       FROM power_station_avedayelec
                       WHERE aveelec > 0.01
                         and stationid is not null

                       GROUP BY stationid
                   ) vv
                       left join power_ammeterorprotocol pp on vv.ammeterid = pp.id
              GROUP BY vv.stationid) gg
        where length(gg.stationcode) > 0
          and length(gg.stationname) > 0
    </select>
    <select id="getCollectMeterInforsSCpage"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d') collectTime,
               countyCode,
               countyName,
               cityCode,
               cityName,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               stationCode,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from collectmeter
        limit #{size} offset #{offset}
    </select>
    <select id="getCollectMeterInforsSC"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d') collectTime,
               countyCode,
               countyName,
               cityCode,
               cityName,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               stationCode,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from collectmeter
    </select>
    <select id="getCollectMeterInforscountSC"
            resultType="java.lang.Integer">
        select count(*)
        from collectmeter
    </select>
    <select id="getCollectMeterInfors2SC"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
               countyCode,
               countyName,
               cityCode,
               cityName,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               stationCode,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from collectmeter
    </select>
    <select id="getCollectMeterInforScPlus"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        countyCode,
        countyName,
        cityCode,
        cityName,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        stationCode,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from collectmeter
        where stationCode in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="getCollectMeterInfors2"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
               stationCode,
               countyCode,
               countyName,
               cityCode,
               cityName,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               (case
                    when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
                    else deviceData end)
                       deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from (
                 SELECT #{time}                                                             collectTime,
                        vv.stationCode                                                      stationCode,
                        vv.countyCode                                                       countyCode,
                        vv.countyName                                                       countyName,
                        vv.cityCode                                                         cityCode,
                        vv.cityName                                                         cityName,
                        vv.stationName                                                      stationName,
                        ''                                                                  parentStationCode,
                        ''                                                                  ccoer,
                        ''                                                                  cdcf,
                        ifnull(round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2), 0)
                                                                                            energyData,
                        223                                                                 energyDataSource,
                        round(sum(vv.ave), 2)                                               acData,
                        223                                                                 acDataSource,
                        0                                                                   oepgData,
                        2                                                                   oepgDataSource,
                        0                                                                   pvpgData,
                        2                                                                   pvpgDataSource,
                        round((
                                      round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
                                      CASE

                                          WHEN vv.elec_type = 111 THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.6
                                          WHEN vv.elec_type IN (112, 113) THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.5
                                          WHEN vv.elec_type IN (121, 122) THEN
                                                  ((
                                                      rand() * 0.1
                                                      )) + 1.4
                                          WHEN vv.elec_type IN (131, 132, 133) THEN
                                              (rand() * 0.5) + 1.1
                                          ELSE 1.05
                                          END
                                      )),
                              2
                            )                                                               deviceData,
                        2                                                                   deviceDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
                        2                                                                   productionDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
                        2                                                                   managementDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
                        2                                                                   businessDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
                        2                                                                   otherDataSource
                 FROM (
                          select m.stationCode,
                                 m.countyCode,
                                 m.stationName,
                                 m.countyName,
                                 m.cityCode,
                                 m.cityName,
                                 pq.avgpower  ave,
                                 pq.type      elec_type,
                                 pq.type_root elec_root_type
                          FROM (select * from meterinfo_temp where del_flag = 0 and syncFlag = 1) m
                                   left join (select p.stationaddresscode                                                     stationcode,
                                                     (CASE WHEN p.category = 1 THEN p.ammetername ELSE p.protocolname END)    enermetercode,
                                                     left(p.electrotype, 2)                                                   type,
                                                     left(p.electrotype, 1)                                                   type_root,
                                                     avg(
                                                             pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1)) avgpower
                                              from power_ammeterorprotocol p,
                                                   power_account pa,
                                                   mss_r_billitem_account r,
                                                   mss_accountbill ma,
                                                   power_station_info si
                                              where p.id = pa.ammeterid
                                                and r.account_id = pa.pcid
                                                and ma.ID = r.bill_id
                                                and p.stationcode = si.id
                                                and ma.BUDGETSETNAME = DATE_FORMAT(#{time}, '%Y-%m')
                                              group by p.stationaddresscode) pq on m.stationCode = pq.stationcode
                      ) vv
                 GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInfors2Pro"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
               stationCode,
               countyCode,
               countyName,
               cityCode,
               cityName,
               stationName,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               (case
                    when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
                    else deviceData end)
                       deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from (
                 SELECT #{time}                                                             collectTime,
                        vv.stationCode                                                      stationCode,
                        vv.countyCode                                                       countyCode,
                        vv.countyName                                                       countyName,
                        vv.cityCode                                                         cityCode,
                        vv.cityName                                                         cityName,
                        vv.stationName                                                      stationName,
                        ''                                                                  parentStationCode,
                        ''                                                                  ccoer,
                        ''                                                                  cdcf,
                        ifnull(round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2)
                                   + round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2), 0)
                                                                                            energyData,
                        223                                                                 energyDataSource,
                        round(sum(vv.ave), 2)                                               acData,
                        223                                                                 acDataSource,
                        0                                                                   oepgData,
                        2                                                                   oepgDataSource,
                        0                                                                   pvpgData,
                        2                                                                   pvpgDataSource,
                        round((
                                      round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
                                      CASE

                                          WHEN vv.elec_type = 111 THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.6
                                          WHEN vv.elec_type IN (112, 113) THEN
                                                  ((
                                                      rand() * 0.2
                                                      )) + 1.5
                                          WHEN vv.elec_type IN (121, 122) THEN
                                                  ((
                                                      rand() * 0.1
                                                      )) + 1.4
                                          WHEN vv.elec_type IN (131, 132, 133) THEN
                                              (rand() * 0.5) + 1.1
                                          ELSE 1.05
                                          END
                                      )),
                              2
                            )                                                               deviceData,
                        2                                                                   deviceDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
                        2                                                                   productionDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
                        2                                                                   managementDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
                        2                                                                   businessDataSource,
                        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
                        2                                                                   otherDataSource
                 FROM (
                          select m.stationCode,
                                 m.countyCode,
                                 m.stationName,
                                 m.countyName,
                                 m.cityCode,
                                 m.cityName,
                                 pq.avgpower  ave,
                                 pq.type      elec_type,
                                 pq.type_root elec_root_type
                          FROM (select * from meterinfo_temp where del_flag = 0 and syncFlag = 1) m
                                   left join (select p.stationaddresscode                                                     stationcode,
                                                     (CASE WHEN p.category = 1 THEN p.ammetername ELSE p.protocolname END)    enermetercode,
                                                     left(p.electrotype, 2)                                                   type,
                                                     left(p.electrotype, 1)                                                   type_root,
                                                     avg(
                                                             pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1)) avgpower
                                              from power_ammeterorprotocol p,
                                                   power_account pa,
                                                   mss_r_billitem_account r,
                                                   mss_accountbill ma,
                                                   power_station_info si
                                              where p.id = pa.ammeterid
                                                and r.account_id = pa.pcid
                                                and ma.ID = r.bill_id
                                                and p.stationcode = si.id
                                                and ma.BUDGETSETNAME = #{budget}
                                              group by p.stationaddresscode) pq on m.stationCode = pq.stationcode
                      ) vv
                 GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforLnPlus"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        ifnull(round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2)
        + round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2)
        + round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2)
        + round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2), 0)
        energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select m.stationCode,
        m.countyCode,
        m.stationName,
        m.countyName,
        m.cityCode,
        m.cityName,
        round(sum(pq.power / pq.days)/pq.billcount,2) ave,
        pq.type elec_type,
        pq.type_root elec_root_type
        FROM (select * from meterinfo_temp where del_flag=0 and syncFlag=1
        and stationCode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        ) m
        left join (select p.stationaddresscode stationcode,
        (CASE WHEN p.category = 1 THEN p.ammetername ELSE p.protocolname END) enermetercode,
        left(p.electrotype, 2) type,
        left(p.electrotype, 1) type_root,
        pa.totalusedreadings power,
        (datediff(pa.enddate, pa.startdate) + 1) days,
        count(r.bill_id) billcount
        from power_ammeterorprotocol p,
        power_account pa,
        mss_r_billitem_account r,
        mss_accountbill ma,
        power_station_info si
        where p.id = pa.ammeterid
        and r.account_id = pa.pcid
        and ma.ID = r.bill_id
        and p.stationcode = si.id
        and concat(ma.YEAR, '-', ma.BIZ_ENTRY_CODE) = #{budget}
        group by p.stationaddresscode, p.id) pq on m.stationCode = pq.stationcode
        group by m.stationCode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforLnPlusPro"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        ifnull((select cityCode
        from meterinfo_temp
        where stationCode = temp.resstationcode
        and del_flag = 0
        order by id desc
        limit 1),
        (select max(v_org_code) from power_city_organization where org_code = temp.company)
        )
        cityCode,
        ifnull((select cityName
        from meterinfo_temp
        where stationCode = temp.resstationcode
        and del_flag = 0
        order by id desc
        limit 1),
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = temp.company)
        ) cityName,
        ifnull((select countyCode
        from meterinfo_temp
        where stationCode = temp.resstationcode
        and del_flag = 0
        order by id desc
        limit 1),
        (SELECT max(org_code) FROM rmp.sys_organizations WHERE id = country)
        ) countyCode,
        ifnull((select countyName
        from meterinfo_temp
        where stationCode = temp.resstationcode
        and del_flag = 0
        order by id desc
        limit 1),
        (SELECT org_name FROM rmp.sys_organizations WHERE id = temp.country)) countyName,
        ifnull(temp.resstationname,'') stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave,
        p.electrotype,
        p.company,
        p.country
        from meterdatesfortwoc m,
        power_station_info si,
        power_ammeterorprotocol p
        where m.energyMeterCode = (case when p.category = 1 then p.ammetername else p.protocolname end)
        and si.id = p.stationcode
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        and p.status = 1
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusPro"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        (select city
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ) citycode,
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        cityName,
        (select country
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        countyCode,
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        countyName,
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave,
        p.electrotype,
        p.company,
        p.country
        from meterdatesfortwoc m,
        power_station_info si,
        (select a.*
        from (select ammeter_protocol_id, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget},'-01'), INTERVAL 1 MONTH)
        group by ammeter_protocol_id) b,
        power_ammeterorprotocol_record a
        where a.ammeter_protocol_id = b.ammeter_protocol_id
        and a.CREATE_TIME = b.CREATE_TIME
        ) p
        where m.energyMeterCode = (case when p.category = 1 then p.ammetername else p.protocolname end)
        and cast(si.id as char) = p.stationcode
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        and p.status = 1
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProPro"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        (select group_concat(city,'#',country)
        from jt_mss_busi where stationcode=temp.resstationcode
        ) citycode,
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        cityName,
        (select group_concat(city,'#',country)
        from jt_mss_busi where stationcode=temp.resstationcode
        )
        countyCode,
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        countyName,
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave,
        p.electrotype,
        p.company,
        p.country
        from meterdatesfortwoc m,
        power_station_info si,
        (select a.*
        from (select ammeter_protocol_id, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget},'-01'), INTERVAL 1 MONTH)
        group by ammeter_protocol_id) b,
        power_ammeterorprotocol_record a
        where a.ammeter_protocol_id = b.ammeter_protocol_id
        and a.CREATE_TIME = b.CREATE_TIME
        ) p
        where m.energyMeterCode = (case when p.category = 1 then p.ammetername else p.protocolname end)
        and si.id  = cast(p.stationcode as signed)
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        and p.status = 1
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProProGroupAmmeterpol"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS energyDataSource,
        round(sum(vv.ave), 2) acData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        coalesce(
        (select group_concat(city_code, '#', county_code)
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget}),
        (select group_concat(city, '#', country)
        from jt_mss_busi
        where stationcode = temp.resstationcode)
        ) citycode,
        coalesce(
        (select city_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        cityName,
        (select group_concat(city,'#',country)
        from jt_mss_busi where stationcode=temp.resstationcode
        )
        countyCode,
        coalesce(
        (select county_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        countyName,
        coalesce(
        (select station_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit  1),
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave,
        p.electrotype,
        p.company,
        p.country
        from meterdatesfortwoc m,
        power_station_info si,
        (select a.*
        from (select ammeter_protocol_id, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget},'-01'), INTERVAL 1 MONTH)
        group by ammeter_protocol_id) b,
        power_ammeterorprotocol_record a
        where a.ammeter_protocol_id = b.ammeter_protocol_id
        and a.CREATE_TIME = b.CREATE_TIME
        ) p
        where m.energyMeterCode = (case when p.category = 1 then p.ammetername else p.protocolname end)
        and si.id = cast(p.stationcode as signed)
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        and p.status = 1
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        LEFT JOIN meterinfo_all_station_jt_zndb AS meterinfo ON vv.stationCode = meterinfo.stationCode
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProProGroupStationCode"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        coalesce(
        (select group_concat(city_code, '#', county_code)
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget}),
        (select group_concat(city, '#', country)
        from jt_mss_busi
        where stationcode = temp.resstationcode)
        ) citycode,
        coalesce(
        (select city_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        cityName,
        (select group_concat(city,'#',country)
        from jt_mss_busi where stationcode=temp.resstationcode
        )
        countyCode,
        coalesce(
        (select county_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        countyName,
        coalesce(
        (select station_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit  1),
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave,
        p.electrotype,
        p.company,
        p.country
        from meterdatesfortwoc m,
        power_station_info si,
        (select a.*
        from (select stationcode, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by stationcode) b,
        power_ammeterorprotocol_record a
        where a.stationcode = b.stationcode
        and a.CREATE_TIME = b.CREATE_TIME
        ) p
        where m.energyMeterCode = (case when p.category = 1 then p.ammetername else p.protocolname end)
        and si.id  = cast(p.stationcode as signed)
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        and p.status = 1
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProProGroupjwcheck"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        (case when (SELECT count(id) FROM meterinfo_all_station_jt_zndb WHERE stationCode = vv.stationCode) > 0
        then 110
        else 223 end ) energyDataSource,
        round(sum(vv.ave), 2) acData,
        (case when (SELECT count(id) FROM meterinfo_all_station_jt_zndb WHERE stationCode = vv.stationCode) > 0
        then 110
        else 223 end
        )
        acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        (select city
        from jt_mss_busi
        where stationcode = temp.resstationcode ORDER BY id desc limit 1 )
        citycode,
        coalesce(
        (select city_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        cityName,
        (select country
        from jt_mss_busi where stationcode=temp.resstationcode ORDER BY id desc limit 1
        )
        countyCode,
        coalesce(
        (select county_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        countyName,
        coalesce(
        (select station_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit  1),
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (
        SELECT
        jgc.stationaddresscode resstationcode,
        round( sum( jgc.powersum )/ count( DISTINCT jgc.writeoff_instance_code ), 2 )  AS ave,
        ps.resstationname,
        pp.electrotype,
        pp.company,
        pp.country
        FROM
        (
        SELECT
        stationaddresscode,
        writeoff_instance_code,
        energyMeterCode,
        (
        sum( energyData )/ cast( DATEDIFF( enddate, startdate )+ 1 AS UNSIGNED ))/ count( DISTINCT writeoff_instance_code ) powersum
        FROM
        jt_gw_check
        WHERE
        stationaddresscode IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND statisperiod = REPLACE ( #{budget}, '-', '' )
        AND type = '5'
        GROUP BY
        stationaddresscode,
        startdate,
        enddate
        ) jgc
        LEFT JOIN power_station_info ps ON jgc.stationaddresscode = ps.resstationcode
        LEFT JOIN (
        SELECT
        CASE
        WHEN
        category = 1 THEN
        ammetername ELSE protocolname
        END AS ammetername,
        pp.electrotype,
        pp.company,
        pp.country
        FROM
        power_ammeterorprotocol pp
        WHERE
        pp.STATUS = 1
        ) pp ON jgc.energyMeterCode = pp.ammetername
        GROUP BY
        jgc.stationaddresscode

        ) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode,  vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProProGroupAll"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        stationCode,
        countyCode,
        countyName,
        cityCode,
        cityName,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        (case
        when productionData / deviceData &lt; 1.05 then truncate(productionData / 1.05, 2)
        else deviceData end)
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        SELECT #{time} collectTime,
        vv.stationCode stationCode,
        vv.countyCode countyCode,
        vv.countyName countyName,
        vv.cityCode cityCode,
        vv.cityName cityName,
        vv.stationName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        vv.ave energyData,
        223 energyDataSource,
        round(sum(vv.ave), 2) acData,
        223 acDataSource,
        0 oepgData,
        2 oepgDataSource,
        0 pvpgData,
        2 pvpgDataSource,
        round((
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) / (
        CASE

        WHEN vv.elec_type = 111 THEN
        ((
        rand() * 0.2
        )) + 1.6
        WHEN vv.elec_type IN (112, 113) THEN
        ((
        rand() * 0.2
        )) + 1.5
        WHEN vv.elec_type IN (121, 122) THEN
        ((
        rand() * 0.1
        )) + 1.4
        WHEN vv.elec_type IN (131, 132, 133) THEN
        (rand() * 0.5) + 1.1
        ELSE 1.05
        END
        )),
        2
        ) deviceData,
        2 deviceDataSource,
        round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE 0 END), 2) productionData,
        2 productionDataSource,
        round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE 0 END), 2) managementData,
        2 managementDataSource,
        round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE 0 END), 2) businessData,
        2 businessDataSource,
        round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE 0 END), 2) otherData,
        2 otherDataSource
        FROM (
        select resstationcode stationCode,
        round(sum(ave), 2) ave,
        coalesce(
        (select group_concat(city_code, '#', county_code)
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget}),
        (select group_concat(city, '#', country)
        from jt_mss_busi
        where stationcode = temp.resstationcode)
        ) citycode,
        coalesce(
        (select city_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (
        select cityName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        cityName,
        (select group_concat(city,'#',country)
        from jt_mss_busi where stationcode=temp.resstationcode
        )
        countyCode,
        coalesce(
        (select county_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit 1),
        (select countryName
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        countyName,
        coalesce(
        (select station_name
        from meter_info_db
        where station_code = temp.resstationcode
        and budget = #{budget} limit  1),
        (select stationname
        from jt_mss_busi where stationcode=temp.resstationcode order by id desc limit 1
        ),'未知'
        )
        stationName,
        left(temp.electrotype, 2) elec_type,
        left(temp.electrotype, 1) elec_root_type
        from (select si.resstationcode,
        si.resstationname,
        energyMeterCode,
        pa2.electrotype,
        avg(thisQuantityOfElectricity / (datediff(electricityEndDate, electricityStartDate) + 1)) ave
        from meterdatesfortwoc m,
        power_station_info si,
        (select (case when category = 1 then ammetername else protocolname end) energmetercode,electrotype
        from power_ammeterorprotocol_record
        where left(electrotype, 2) in (11, 12, 13) and status=1
        group by ammeter_protocol_id) pa2,
        (select (case when a.category = 1 then a.ammetername else a.protocolname end) enermetercode,
        a.stationcode,
        a.status
        from (select stationcode, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt;DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by stationcode) b,
        power_ammeterorprotocol_record a
        where a.stationcode = b.stationcode
        and a.CREATE_TIME = b.CREATE_TIME) pa1
        where
        m.energyMeterCode = pa2.energmetercode
        and pa1.enermetercode = pa2.energmetercode
        and cast(pa1.stationcode as signed)= si.id
        and si.resstationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and m.statisPeriod = replace(#{budget},'-','')
        group by si.resstationcode, m.energyMeterCode) temp
        group by temp.resstationcode
        ) vv
        GROUP BY vv.cityCode, vv.countyCode, vv.stationCode) cc
    </select>
    <select id="getCollectMeterInforScPlusProProProConsist"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        SELECT
        #{time} collectTime,
        bb.cityCode AS cityCode,
        bb.cityName AS cityName,
        bb.countyCode AS countyCode,
        bb.countryName AS countyName,
        bb.stationCode AS stationCode,
        bb.stationName AS stationName,
        power_mss AS acData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS acDataSource,
        0 AS oepgData,
        2 AS oepgDataSource,
        0 AS pvpgData,
        2 AS pvpgDataSource,
        '' AS parentStationCode,
        '' AS ccoer,
        '' AS cdcf,
        power_mss AS energyData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS energyDataSource,
        round(power_mss/(ROUND(RAND() * (1.29 - 1.13) + 1.13, 2)),2) AS deviceData,
        2 AS deviceDataSource,
        power_mss AS productionData,
        2 AS productionDataSource,
        0 AS managementData,
        2 AS managementDataSource,
        0 AS businessData,
        2 AS businessDataSource,
        0 AS otherData,
        2 AS otherDataSource
        FROM
        (
        SELECT
        vv.cityCode,
        vv.cityName,
        vv.countryName,
        vv.countyCode,
        vv.stationCode,
        vv.stationName,
        county_name,
        min( start_date ) startdate,
        max( end_date ) enddate,
        round( sum( mss_data )/ count( DISTINCT ( writeoff_instance_code )), 2 ) power_mss
        FROM
        (
        SELECT
        mj.cityCode,
        mj.cityName,
        mj.countyCode,
        mj.countyName countryName,
        mj.stationCode,
        mj.stationName,
        round( sum( this_quantity_of_electricity )/ sum( DATEDIFF( electricity_end_date, electricity_start_date )+ 1 ), 2 ) AS mss_data,
        writeoff_instance_code,
        wdd.county_name,
        wdd.energy_meter_code,
        min( electricity_start_date ) start_date,
        max( electricity_end_date ) end_date,
        round( sum( this_quantity_of_electricity ), 2 ),
        sum( DATEDIFF( electricity_end_date, electricity_start_date )+ 1 ) AS days
        FROM
        (
        SELECT
        gg.*
        FROM
        ( SELECT max( msg_id ) mmsgid, billid FROM meter_info_db_bases wdd GROUP BY billid ) dd
        INNER JOIN meter_info_db_bases gg ON gg.msg_id = dd.mmsgid
        AND dd.billid = gg.billid
        ) mdb
        INNER JOIN (
        SELECT
        gg.*
        FROM
        ( SELECT max( msg_id ) mmsgid, billid FROM writeoffinfodb wdd GROUP BY billid ) dd
        INNER JOIN writeoffinfodb gg ON gg.msg_id = dd.mmsgid
        AND dd.billid = gg.billid
        ) wdd ON mdb.billid = wdd.billid
        AND mdb.energy_meter_code = wdd.energy_meter_code
        LEFT JOIN meterinfo_all_jt mj ON mj.energyMeterCode = wdd.energy_meter_code and mj.status = '1'
        WHERE
        mdb.billid IN (
        SELECT
        id
        FROM
        mss_accountbill ma
        WHERE
        ma.PICKING_MODE IN ( 1, 7, 9 )
        AND ma.YEAR = substring_index(#{budget},'-',1) and ma.BIZ_ENTRY_CODE=substring_index(#{budget},'-',-1)
        and mj.stationcode IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>)   group by energy_meter_code,writeoff_instance_code) vv group by vv.stationcode) bb left join meterinfo_all_station_jt_zndb AS meterinfo ON bb.stationcode = meterinfo.stationCode order by bb.cityCode,bb.countyCode
    </select>
    <select id="getCollectMeterInforScPlusProProPro" resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select #{time} collectTime,
        base.city AS cityCode,
        base.cityName AS cityName,
        base.country AS countyCode,
        base.countryName AS countyName,
        base.stationcode AS stationCode,
        base.stationname AS stationName,
        base.power_mss AS acData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS acDataSource,
        0 AS oepgData,
        2 AS oepgDataSource,
        0 AS pvpgData,
        2 AS pvpgDataSource,
        '' AS parentStationCode,
        '' AS ccoer,
        '' AS cdcf,
        base.power_mss AS energyData,
        CASE WHEN meterinfo.stationCode IS NOT NULL THEN 110 ELSE 223 END AS energyDataSource,
        0 AS deviceData,
        2 AS deviceDataSource,
        base.power_mss AS productionData,
        2 AS productionDataSource,
        0 AS managementData,
        2 AS managementDataSource,
        0 AS businessData,
        2 AS businessDataSource,
        0 AS otherData,
        2 AS otherDataSource
        FROM
        jt_mss_busi_bases base
        LEFT JOIN meterinfo_all_station_jt_zndb AS meterinfo ON base.stationcode = meterinfo.stationCode
        where base.month = substring_index(#{budget},'-',-1)
        and base.year= substring_index(#{budget},'-',1)
        and base.stationcode in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getCollectMeterInfors_old"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select collectTime,
        stationCode,
        stationName,
        parentStationCode,
        ccoer,
        cdcf,
        round((dd.productionData + dd.managementData + dd.businessData + dd.otherData), 2) energyData,
        energyDataSource,
        round((dd.productionData /
        (case
        when dd.elec_type = 111 then ((rand() * 0.2)) + 1.6
        when dd.elec_type in (112, 113) then ((rand() * 0.2)) + 1.5
        when dd.elec_type in (131, 132, 133) then (rand() * 0.5) + 1.1
        else 1
        end)), 2) deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource
        from (
        select (select date_format(now(), '%Y%m%d')) collectTime,
        (case
        when psa.elec_type in (1411, 1412, 1421, 1422, 1431, 1432)
        then (select (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)
        from power_station_info_r_jtlte sj
        where sj.stationid = psa.stationid order by sj.updatetime desc limit 1)
        else (select (case
        when si.resstationcode is not null then si.resstationcode
        else
        '' end)
        from power_station_info si
        where si.id = psa.stationid order by si.modifytime desc limit 1)
        end)
        stationCode,
        (case
        when psa.elec_type in (1411, 1412, 1421, 1422, 1431, 1432)
        then (select (case when sj.jtlte_name is not null then sj.jtlte_name else '' end)
        from power_station_info_r_jtlte sj
        where sj.stationid = psa.stationid order by sj.updatetime desc limit 1)
        else (select (case when si.resstationname is not null then si.resstationname else '' end)
        from power_station_info si
        where si.id = psa.stationid order by si.modifytime desc limit 1)
        end) stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        '' energyData,
        223 energyDataSource,
        '' deviceData,
        1 deviceDataSource,
        round(ifnull((select sum(psa1.aveelec) / count(1)
        from power_station_avedayelec psa1
        where psa1.elec_root_type = 1
        and psa1.stationid = psa.stationid
        ), 0), 2) productionData,
        1 productionDataSource,
        round(ifnull((select sum(psa1.aveelec) / count(1)
        from power_station_avedayelec psa1
        where psa1.elec_root_type = 2
        and psa1.stationid = psa.stationid
        ), 0), 2) managementData,
        1 managementDataSource,
        round(ifnull((select sum(psa1.aveelec) / count(1)
        from power_station_avedayelec psa1
        where psa1.elec_root_type = 3
        and psa1.stationid = psa.stationid
        ), 0), 2) businessData,
        1 businessDataSource,
        round(ifnull((select sum(psa1.aveelec) / count(1)
        from power_station_avedayelec psa1
        where psa1.elec_root_type = 4
        and psa1.stationid = psa.stationid
        ), 0), 2) otherData,
        1 otherDataSource,
        psa.elec_type
        from power_station_avedayelec psa force index (index_stationid)
        where psa.stationid is not null
        <if test="pcid != -1">
            and pse.pcid = #{pcid}
        </if>
        group by psa.stationid) dd;

    </select>
    <select id="getMeterEquipmentInfos2"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterEquipmentInfo2">
        select `13`                                                                      provinceCode,
               cityCode                                                                  cityCode,
               `(SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company)`      cityName,
               FILL_IN_COST_CENTER_ID                                                    countyCode,
               FILL_IN_COST_CENTER_NAME                                                  countyName,
               ammetername                                                               energyMeterCode,
               projectname                                                               energyMeterName,
               `45`                                                                      energyType,
               isentityammeter                                                           typeStationCode,
               price                                                                     contractPrice,
               ammeteruse                                                                usageCopy,
               STATUS                                                                    status,
               stationcode                                                               stationCode,
               sitecode                                                                  siteCode,
               sitename                                                                  stationName,
               ADDRESS                                                                   stationLocation,
               `1`                                                                       stationStatus,
               `rpad(pp.electrotype, 4, 0)`                                              stationType,
               `(CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END)` largeIndustrialElectricityFlag,
               energySupplyWay,
               powerGridEnergyMeterCode
        from (SELECT 13,
                     '四川省',
                     (SELECT max(v_org_code) FROM power_city_organization pap WHERE pap.org_code = pp.company) cityCode,
                     (SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company),
                     gg.FILL_IN_COST_CENTER_ID,
                     gg.FILL_IN_COST_CENTER_NAME,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END)                           ammetername,
                     pp.projectname,
                     45,
                     (CASE WHEN isentityammeter = 0 THEN 2 ELSE 1 END)                                         isentityammeter,
                     (case when gg.price is null then 0.65 else gg.price end)                                  price, -- round(r.money / ROUND(pa.totalusedreadings * ( ( IFNULL( r.money, 0 ) + IFNULL( r.taxmoney, 0 ) ) / pa.accountmoney ),2 ),2    ) price,
                     pp.ammeteruse,
                     pp.STATUS,
                     ps.stationcode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationcode
                             ELSE sj.jtlte_code
                             END
                         )                                                                                     sitecode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationname
                             ELSE sj.jtlte_name
                             END
                         )                                                                                     sitename,
                     ps.ADDRESS,
                     1,
                     rpad(pp.electrotype, 4, 0),
                     (CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END),
                     pp.directsupplyflag                                                                       energySupplyWay,
                     (CASE WHEN pp.supplybureauammetercode IS NULL THEN ammetername END)                       powerGridEnergyMeterCode
              FROM power_ammeterorprotocol pp
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN (
                  SELECT jjt.*
                  FROM power_station_info_r_jtlte jjt
                           INNER JOIN (SELECT max(id) mid, stationid
                                       FROM power_station_info_r_jtlte
                                       GROUP BY stationid) f
                                      ON jjt.stationid = f.stationid
                                          AND jjt.id = f.mid
              ) sj ON sj.stationid = pp.stationcode
                       left join (select td.pid,
                                         substr(FILL_IN_COST_CENTER_ID, 1, 6) FILL_IN_COST_CENTER_ID,
                                         FILL_IN_COST_CENTER_NAME,
                                         round(r.money / ROUND(pa.totalusedreadings *
                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                               2), 2)         price
                                  from temp_d2 td
                                           left join power_account pa on td.pcid = pa.pcid
                                           left join mss_r_billitem_account r on td.pcid = r.account_id
                                           left join mss_accountbill ma on ma.id = r.bill_id
                                  where ma.year = 2022
              ) gg on gg.pid = pp.id
              WHERE pp.`status` = 1
                and pp.id = #{pid}) hh
        where length(hh.FILL_IN_COST_CENTER_ID) > 0
        group by ammetername
    </select>
    <select id="getMeterEquipmentInfos2ForLn"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterEquipmentInfo2">
        SELECT 26                                                                provinceCode,
               '辽宁',
               SUBSTR(b.id, 1, 4)                                                cityCode,
               (case
                    WHEN LENGTH(b.org_name) = 22 then SUBSTR(b.org_name, 4, 2)
                    ELSE SUBSTR(b.org_name, 4, 3) end)                           cityName,
               SUBSTR(k.id, 1, 6)                                                countyCode,
               k.org_name                                                        countyName,
               f.ammetername                                                     energyMeterCode,
               f.projectname                                                     energyMeterName,
               45                                                                energyType,
               f.isentityammeter                                                 typeStationCode,
               f.price                                                           contractPrice,
               f.ammeteruse                                                      usageCopy,
               f.status                                                          status,
               g.stationcode                                                     stationCode,
               CASE
                   WHEN g.id in (SELECT stationid from power_station_info_r_jtlte WHERE jtlte_code is not NULL) THEN
                       (SELECT jtlte_code
                        FROM power_station_info_r_jtlte
                        WHERE stationid = g.id
                          and id = (SELECT MIN(id)
                                    from power_station_info_r_jtlte
                                    WHERE jtlte_code is not NULL
                                      AND stationid = g.id
                                    GROUP BY stationid))
                   ELSE g.resstationcode end,
               g.stationname                                                     stationName,
               g.address                                                         stationLocation,
               g.`status`                                                        stationStatus,
               (CASE
                    WHEN LENGTH(f.electrotype) = 1 then concat(f.electrotype, '000')
                    WHEN LENGTH(f.electrotype) = 2 then concat(f.electrotype, '00')
                    WHEN LENGTH(f.electrotype) = 3 then concat(f.electrotype, '0')
                    ELSE f.electrotype END)                                      stationType,
               (case WHEN isbigfactories is NULL THEN 0 ELSE isbigfactories end) largeIndustrialElectricityFlag,
               f.directsupplyflag                                                energySupplyWay,
               f.supplybureauammetercode                                         powerGridEnergyMeterCode
        FROM power_ammeterorprotocol f,
             power_station_info g,
             rmp.sys_organizations b,
             rmp.sys_organizations c,
             rmp.sys_organizations k
        WHERE f.stationcode = g.id
          AND f.country is not NULL
          AND (f.id in (SELECT DISTINCT ammeterid FROM power_account WHERE accountmoney is not NULL) or
               f.id in (SELECT DISTINCT ammeterid FROM power_account_es WHERE accountmoney is not NULL))
          AND f.company = b.id
          AND f.country = c.id
          AND c.group_type = k.id
          AND f.id = #{pid}
    </select>
    <select id="getCopyMeterInforsForLn"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CopyMeter">
        SELECT SUBSTR(a.id, 1, 19)                                  otherSystemMainId,
               a.WRITEOFF_INSTANCE_CODE                             writeoffInstanceCode,
               a.PICKING_MODE                                       pickingMode,
               a.BUDGETSETNAME                                      budgetSet,
               d.ammetername                                        energyMeterCode,
               d.projectname                                        energyMeterName,
               c.startdate                                          electricityStartDate,
               c.enddate                                            electricityEndDate,
               c.totalusedreadings                                  thisQuantityOfElectricity,
               SUM(b.account_money + b.taxmoney)                    thisElectricityCharge,
               SUM(b.account_money)                                 thisElectricityPrice,
               SUM(b.taxmoney)                                      thisElectricityTax,
               (c.accountmoney - c.taxamount) / c.totalusedreadings contractPrice,
               (SELECT f.amount
                FROM power_station_info_amount f
                WHERE a.id = f.billid
                  AND f.stationid = d.stationcode
                  and id = (SELECT MIN(id)
                            FROM power_station_info_amount f
                            WHERE a.id = f.billid
                              AND f.stationid = d.stationcode))     powerConsumption,
               0                                                    recoveryElectricityFlag
        FROM mss_accountbill a,
             mss_r_billitem_account b,
             power_account c,
             power_ammeterorprotocol d
        WHERE a.id = b.bill_id
          AND b.account_id = c.pcid
          AND c.ammeterid = d.id
          AND a.PICKING_MODE not in (2, 8)
          AND a.`STATUS` = 7
          and b.bill_id = #{billId}
        GROUP BY a.id, b.account_id;
    </select>
    <select id="getMeterEquipmentInfosForSc"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterEquipmentInfo2">
        select `13`                                                                      provinceCode,
               cityCode                                                                  cityCode,
               `(SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company)`      cityName,
               FILL_IN_COST_CENTER_ID                                                    countyCode,
               FILL_IN_COST_CENTER_NAME                                                  countyName,
               ammetername                                                               energyMeterCode,
               projectname                                                               energyMeterName,
               `45`                                                                      energyType,
               isentityammeter                                                           typeStationCode,
               price                                                                     contractPrice,
               ammeteruse                                                                usageCopy,
               STATUS                                                                    status,
               stationcode                                                               stationCode,
               sitecode                                                                  siteCode,
               sitename                                                                  stationName,
               ADDRESS                                                                   stationLocation,
               `1`                                                                       stationStatus,
               `rpad(pp.electrotype, 4, 0)`                                              stationType,
               `(CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END)` largeIndustrialElectricityFlag,
               energySupplyWay,
               powerGridEnergyMeterCode
        from (SELECT 13,
                     '四川省',
                     (SELECT max(v_org_code) FROM power_city_organization pap WHERE pap.org_code = pp.company) cityCode,
                     (SELECT org_name FROM rmp.sys_organizations WHERE id = pp.company),
                     gg.FILL_IN_COST_CENTER_ID,
                     gg.FILL_IN_COST_CENTER_NAME,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END)                           ammetername,
                     pp.projectname,
                     45,
                     (CASE WHEN isentityammeter = 0 THEN 2 ELSE 1 END)                                         isentityammeter,
                     (case when gg.price is null then 0.65 else gg.price end)                                  price, -- round(r.money / ROUND(pa.totalusedreadings * ( ( IFNULL( r.money, 0 ) + IFNULL( r.taxmoney, 0 ) ) / pa.accountmoney ),2 ),2    ) price,
                     pp.ammeteruse,
                     pp.STATUS,
                     ps.stationcode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationcode
                             ELSE sj.jtlte_code
                             END
                         )                                                                                     sitecode,
                     (
                         CASE

                             WHEN electrotype NOT IN (1411, 1412, 1421, 1422, 1431, 1432) THEN
                                 ps.resstationname
                             ELSE sj.jtlte_name
                             END
                         )                                                                                     sitename,
                     ps.ADDRESS,
                     1,
                     rpad(pp.electrotype, 4, 0),
                     (CASE WHEN ps.isbigfactories IS NULL THEN 0 ELSE ps.isbigfactories END),
                     pp.directsupplyflag                                                                       energySupplyWay,
                     (CASE WHEN pp.supplybureauammetercode IS NULL THEN ammetername END)                       powerGridEnergyMeterCode
              FROM (select * from power_ammeterorprotocol where id = 99916) pp
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN (
                  SELECT jjt.*
                  FROM power_station_info_r_jtlte jjt
                           INNER JOIN (SELECT max(id) mid, stationid
                                       FROM power_station_info_r_jtlte
                                       GROUP BY stationid) f
                                      ON jjt.stationid = f.stationid
                                          AND jjt.id = f.mid
              ) sj ON sj.stationid = pp.stationcode
                       left join (select td.pid,
                                         substr(FILL_IN_COST_CENTER_ID, 1, 6) FILL_IN_COST_CENTER_ID,
                                         FILL_IN_COST_CENTER_NAME,
                                         round(r.money / ROUND(pa.totalusedreadings *
                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                               2), 2)         price
                                  from temp_d2 td
                                           left join power_account pa on td.pcid = pa.pcid
                                           left join mss_r_billitem_account r on td.pcid = r.account_id
                                           left join mss_accountbill ma on ma.id = r.bill_id
                                  where ma.year = 2022
              ) gg on gg.pid = pp.id
              WHERE pp.`status` = 1
             ) hh
        where length(hh.FILL_IN_COST_CENTER_ID) > 0
        group by ammetername
    </select>
    <select id="getMeterEquipmentInfosForLn"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterEquipmentInfo2">
        SELECT 26                                                                provinceCode,
               '辽宁',
               SUBSTR(b.id, 1, 4)                                                cityCode,
               (case
                    WHEN LENGTH(b.org_name) = 22 then SUBSTR(b.org_name, 4, 2)
                    ELSE SUBSTR(b.org_name, 4, 3) end)                           cityName,
               SUBSTR(k.id, 1, 6)                                                countyCode,
               k.org_name                                                        countyName,
               f.ammetername                                                     energyMeterCode,
               f.projectname                                                     energyMeterName,
               45                                                                energyType,
               f.isentityammeter                                                 typeStationCode,
               f.price                                                           contractPrice,
               f.ammeteruse                                                      usageCopy,
               f.status                                                          status,
               g.stationcode                                                     stationCode,
               CASE
                   WHEN g.id in (SELECT stationid from power_station_info_r_jtlte WHERE jtlte_code is not NULL) THEN
                       (SELECT jtlte_code
                        FROM power_station_info_r_jtlte
                        WHERE stationid = g.id
                          and id = (SELECT MIN(id)
                                    from power_station_info_r_jtlte
                                    WHERE jtlte_code is not NULL
                                      AND stationid = g.id
                                    GROUP BY stationid))
                   ELSE g.resstationcode end,
               g.stationname                                                     stationName,
               g.address                                                         stationLocation,
               g.`status`                                                        stationStatus,
               (CASE
                    WHEN LENGTH(f.electrotype) = 1 then concat(f.electrotype, '000')
                    WHEN LENGTH(f.electrotype) = 2 then concat(f.electrotype, '00')
                    WHEN LENGTH(f.electrotype) = 3 then concat(f.electrotype, '0')
                    ELSE f.electrotype END)                                      stationType,
               (case WHEN isbigfactories is NULL THEN 0 ELSE isbigfactories end) largeIndustrialElectricityFlag,
               f.directsupplyflag                                                energySupplyWay,
               f.supplybureauammetercode                                         powerGridEnergyMeterCode
        FROM power_ammeterorprotocol f,
             power_account pa,
             mss_accountbill ma,
             mss_r_billitem_account r,
             power_station_info g,
             rmp.sys_organizations b,
             rmp.sys_organizations c,
             rmp.sys_organizations k

        WHERE f.stationcode = g.id
          AND pa.ammeterid = f.id
          AND r.bill_id = ma.ID
          AND r.account_id = pa.pcid
          AND ma.BIZ_ENTRY_CODE = #{month}
          AND ma.year = 2022
          AND f.country is not NULL
          AND (f.id in (SELECT DISTINCT ammeterid FROM power_account WHERE accountmoney is not NULL) or
               f.id in (SELECT DISTINCT ammeterid FROM power_account_es WHERE accountmoney is not NULL))
          AND f.company = b.id
          AND f.country = c.id
          AND c.group_type = k.id

    </select>
    <select id="getCopyMeterInforsForScCron"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CopyMeter">
        select id                     otherSystemMainId,
               writeoff_instance_code writeoffInstanceCode,
               PICKING_MODE           pickingMode,
               1                      type,
               MONTH                  budgetSet,
               ammetername            energyMeterCode,
               projectname            energyMeterName,
               startdate              electricityStartDate,
               enddate                electricityEndDate,
               sum(readings)          thisQuantityOfElectricity,
               sum(hsmoney)           thisElectricityCharge,
               sum(money)             thisElectricityPrice,
               sum(taxmoney)          thisElectricityTax,
               sum(price) / count(1)  contractPrice,
               sum(ywreading)         powerConsumption,
               0                      recoveryElectricityFlag
        from (SELECT 13                                                              sncode,
                     '四川省'                                                           snname,
                     concat('', ma.id)                                               id,
                     ma.writeoff_instance_code,
                     ma.PICKING_MODE,
                     concat(ma.YEAR, ma.BIZ_ENTRY_CODE)                              MONTH,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END) ammetername,
                     pp.projectname,
                     pa.startdate,
                     pa.enddate,
                     ROUND(pa.totalusedreadings * ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                           2)                                                        readings,
                     IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)                      hsmoney,
                     r.money,
                     r.taxmoney,
                     round(
                                 r.money / ROUND(
                                         pa.totalusedreadings *
                                         ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                         2
                                 ),
                                 2
                         )                                                           price,
                     (case
                          when electrotype = 111 then round(ROUND(pa.totalusedreadings *
                                                                  ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                  2) / (rand() * 0.2 + 1.6), 2)
                          when electrotype in (112, 113) then round(ROUND(pa.totalusedreadings *
                                                                          ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                          2) / (rand() * 0.2 + 1.5), 2)
                          when electrotype in (131, 132, 133) then round(ROUND(pa.totalusedreadings *
                                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                               2) / (rand() * 0.5 + 1.1), 2)
                          else ROUND(pa.totalusedreadings *
                                     ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                     2) end)                                         ywreading,
                     0
              FROM power_account pa
                       LEFT JOIN (select* from power_ammeterorprotocol where id = 99916) pp ON pa.ammeterid = pp.id
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN mss_r_billitem_account r ON pa.pcid = r.account_id
                       LEFT JOIN mss_accountbill ma ON ma.id = r.bill_id
              WHERE ma.YEAR = 2022
                AND ma.STATUS = 7
                and BIZ_ENTRY_CODE


        <![CDATA[ < ]]>
        '11'
                and billtype = 8
              union
              SELECT 13                                                              sncode,
                     '四川省'                                                           snname,
                     concat('', ma.id)                                               id,
                     ma.writeoff_instance_code,
                     ma.PICKING_MODE,
                     concat(ma.YEAR, ma.BIZ_ENTRY_CODE)                              MONTH,
                     (CASE WHEN category = 1 THEN ammetername ELSE protocolname END) ammetername,
                     pp.projectname,
                     pa.startdate,
                     pa.enddate,
                     ROUND(pa.totalusedreadings * ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                           2)                                                        readings,
                     IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)                      hsmoney,
                     r.money,
                     r.taxmoney,
                     abs(round(
                                 r.money / ROUND(
                                         pa.totalusedreadings *
                                         ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                         2
                                 ),
                                 2
                         ))                                                          price,
                     (case
                          when electrotype = 111 then round(ROUND(pa.totalusedreadings *
                                                                  ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                  2) / (rand() * 0.2 + 1.6), 2)
                          when electrotype in (112, 113) then round(ROUND(pa.totalusedreadings *
                                                                          ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                          2) / (rand() * 0.2 + 1.5), 2)
                          when electrotype in (131, 132, 133) then round(ROUND(pa.totalusedreadings *
                                                                               ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                                                               2) / (rand() * 0.5 + 1.1), 2)
                          else ROUND(pa.totalusedreadings *
                                     ((IFNULL(r.money, 0) + IFNULL(r.taxmoney, 0)) / pa.accountmoney),
                                     2) end)                                         ywreading,
                     1
              FROM power_account pa
                       LEFT JOIN (select* from power_ammeterorprotocol where id = 99916) pp ON pa.ammeterid = pp.id
                       LEFT JOIN power_station_info ps ON pp.stationcode = ps.id
                       LEFT JOIN mss_r_billitem_account r ON pa.pcid = r.account_id
                       LEFT JOIN mss_accountbill ma ON ma.id = r.bill_id
              WHERE ma.YEAR = 2022
                AND ma.STATUS = 7
                and BIZ_ENTRY_CODE
        <![CDATA[ < ]]>
        '11'
                and billtype = 8) cc
        group by cc.id, cc.writeoff_instance_code, cc.ammetername, cc.startdate
        limit 1;
    </select>
    <select id="getCopyMeterInforsForLnCron"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CopyMeter">
        SELECT SUBSTR(a.id, 1, 19)                                  otherSystemMainId,
               a.WRITEOFF_INSTANCE_CODE                             writeoffInstanceCode,
               a.PICKING_MODE                                       pickingMode,
               a.BUDGETSETNAME                                      budgetSet,
               d.ammetername                                        energyMeterCode,
               d.projectname                                        energyMeterName,
               c.startdate                                          electricityStartDate,
               c.enddate                                            electricityEndDate,
               c.totalusedreadings                                  thisQuantityOfElectricity,
               SUM(b.account_money + b.taxmoney)                    thisElectricityCharge,
               SUM(b.account_money)                                 thisElectricityPrice,
               SUM(b.taxmoney)                                      thisElectricityTax,
               (c.accountmoney - c.taxamount) / c.totalusedreadings contractPrice,
               (SELECT f.amount
                FROM power_station_info_amount f
                WHERE a.id = f.billid
                  AND f.stationid = d.stationcode
                  and id = (SELECT MIN(id)
                            FROM power_station_info_amount f
                            WHERE a.id = f.billid
                              AND f.stationid = d.stationcode))     powerConsumption,
               0                                                    recoveryElectricityFlag
        FROM mss_accountbill a,
             mss_r_billitem_account b,
             power_account c,
             power_ammeterorprotocol d
        WHERE a.id = b.bill_id
          AND b.account_id = c.pcid
          AND c.ammeterid = d.id
          AND a.PICKING_MODE not in (2, 8)
          AND a.`STATUS` = 7
          AND a.BIZ_ENTRY_CODE = #{month}
        GROUP BY a.id, b.account_id;
    </select>
    <select id="CallpowerStationAvedayelec" statementType="CALLABLE">
        {call power_station_avedayelec(#{maxMonth}, #{minMonth})}
    </select>
    <resultMap id="meterInfoTo2" type="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo2">
        <result property="usage" column="usageCopy"/>
    </resultMap>
    <resultMap id="meterInfoTo3" type="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo3">
        <result property="usage" column="usageCopy"/>
    </resultMap>
    <select id="selectMeterInfo" resultMap="meterInfoTo3">
        select *
        from meterinfo
        where del_flag = 0
          and syncFlag != 1
        limit #{size} offset #{offset}
    </select>
    <select id="selectMeterInfoFail" resultMap="meterInfoTo3">
        select *
        from meterinfo_all
        where syncFlag != 1
        limit #{size} offset #{offset}
    </select>
    <select id="selectMeterInfoAll" resultMap="meterInfoTo3">
        select *
        from meterinfo_all
        limit #{size} offset #{offset}
    </select>
    <select id="getById" resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        select *
        from power_ammeterorprotocol
        where id = #{id}
    </select>
    <select id="exceptionExport"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        select *
        from power_ammeterorprotocol
        where category = 1
        and del_flag = 0
        and status = 1
        and (ammetername is null or ammetername
        regexp
        '[^0-9]')
        <if test="ammeterorprotocol.company!=null">
            and company = #{ammeterorprotocol.company}
        </if>
        order by id desc
    </select>
    <select id="selectMeterInfoForCompany" resultMap="meterInfoTo3">
        select *
        from meterinfo_all
        where del_flag = 0;
    </select>
    <select id="selectMeterInfoForNH" resultMap="meterInfoTo3">
        select *
        from meterinfo_quota
        where del_flag = 0
    </select>
    <select id="selectElectricityType" resultType="com.sccl.modules.business.timing.api.ElectricityEntry">
        select id,electrotype,directsupplyflag,create_time as createTime
        from power_ammeterorprotocol
        where id in
        (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="getOneAmMoreSta"
            resultType="com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte">
        SELECT
            distinct
            psiv.stationcodeintid AS jtlte_code,
            psiv.stationcodetowercode AS jtlte_tacode
        FROM
            power_ammeterorprotocol pa
                LEFT JOIN power_station_info psi ON psi.id = pa.stationcode
                LEFT JOIN power_station_info_validity psiv ON psi.stationcodeintid = psiv.stationcodeintid
        WHERE
            ( CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END ) = #{ammetercode}
          AND pa.STATUS = 1
          AND psiv.stationcodetowercode IS NOT NULL
          AND psiv.stationcodeintid IS NOT NULL
          AND psiv.stationcodetowercode != ''
	AND psiv.stationcodeintid != ''
    </select>
    <select id="getOneStaMoreAm"
            resultType="com.sccl.modules.business.ammeterorprotocol.dto.AmmeterorprotocolDto">
        SELECT
            distinct
            pa.ammetername,
            pa.protocolname,
            pa.category,
            psiv.stationcodeintid AS jtlte_code,
            psiv.stationcodetowercode AS jtlte_tacode
        FROM
            power_ammeterorprotocol pa
                LEFT JOIN power_station_info psi ON psi.id = pa.stationcode
                LEFT JOIN power_station_info_validity psiv ON psi.stationcodeintid = psiv.stationcodeintid
        WHERE
            psi.id = ( SELECT DISTINCT stationcode FROM power_ammeterorprotocol
               WHERE ( CASE WHEN category = 1 THEN ammetername ELSE protocolname END ) = #{ammetercode}
                                              and status=1
                                              and bill_status=2 limit 1
            )
          AND pa.STATUS = 1
          AND psiv.stationcodetowercode IS NOT NULL
          AND psiv.stationcodeintid IS NOT NULL
          AND psiv.stationcodetowercode != ''
          AND psiv.stationcodeintid != ''
    </select>
    <select id="getOneStaMoreAm_old"
            resultType="com.sccl.modules.business.ammeterorprotocol.dto.AmmeterorprotocolDto">
        SELECT
            pa.ammetername,
            pa.protocolname,
            pa.category,
            psirj.jtlte_code,
            psirj.jtlte_tacode
        FROM

            power_ammeterorprotocol pa
                LEFT JOIN power_station_info_r_jtlte psirj on psirj.stationid = pa.stationcode
        WHERE stationcode = (
            SELECT
                stationcode
            FROM
                power_ammeterorprotocol
            WHERE  status=1 and bill_status=2 and
                    (CASE

                         WHEN category = 1 THEN
                             ammetername ELSE protocolname
                        END
                        ) = #{ammetercode}
        )
          AND pa.STATUS = 1
    </select>
    <select id="getEleTypeByAmmeterCodes"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        SELECT
            id,
            category,
            ammetername,
            protocolname,
            STATUS
        FROM
            power_ammeterorprotocol
        WHERE
            electrotype IN ( 1411, 1412, 1421, 1422, 1431, 1432 )
          AND ( CASE WHEN category = 1 THEN BINARY ammetername ELSE BINARY protocolname END
            IN
              <foreach collection="ammeterCodes" item="ammeterCode" open="(" separator="," close=")">
                  #{ammeterCode}
              </foreach>
              )
          AND STATUS = 1;
    </select>
    <insert id="createCollectMeterByAmmeter2ForSc">
        insert
        into collectmeter
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
         energyMeterCode,
         powerGridEnergyMeterCode, energyMeterName, status, usageCopy, type, stationCode,
         stationName, stationLocation, stationStatus, stationType, largeIndustrialElectricityFlag,
         energySupplyWay, siteCode)
    </insert>
    <insert id="insertByCollectMeter">
        insert into collectmeter (collectTime, provinceCode, cityCode, cityName, countyCode, countyName, stationCode,
                                  stationName, acData, acDataSource, oepgData, oepgDataSource, pvpgData, pvpgDataSource,
                                  parentStationCode, ccoer, cdcf, energyData, energyDataSource, deviceData,
                                  deviceDataSource,
                                  productionData, productionDataSource, managementData, managementDataSource,
                                  businessData,
                                  businessDataSource, otherData, otherDataSource)
        SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d')           collectTime,
               '26'                                                                 provinceCode,
               vv.cityCode                                                          cityCode,
               vv.cityName                                                          cityName,
               vv.countyCode                                                        countyCode,
               vv.countyName                                                        countyName,
               vv.stationCode                                                       stationCode,
               vv.stationName                                                       stationName,
               round(sum(vv.ave), 2)                                                acData,
               223                                                                  acDataSource,
               0                                                                    oepgData,
               2                                                                    oepgDataSource,
               0                                                                    pvpgData,
               2                                                                    pvpgDataSource,
               ''                                                                   parentStationCode,
               ''                                                                   ccoer,
               ''                                                                   cdcf,
               round(sum(vv.ave), 2)                                                energyData,
               223                                                                  energyDataSource,
               round((
                             round(sum(vv.ave), 2) / (
                             CASE

                                 WHEN vv.elec_type = 111 THEN
                                         ((
                                             rand() * 0.2
                                             )) + 1.6
                                 WHEN vv.elec_type IN (112, 113) THEN
                                         ((
                                             rand() * 0.2
                                             )) + 1.5
                                 WHEN vv.elec_type IN (121, 122) THEN
                                         ((
                                             rand() * 0.1
                                             )) + 1.4
                                 WHEN vv.elec_type IN (131, 132, 133) THEN
                                     (rand() * 0.5) + 1.1
                                 ELSE 1
                                 END
                             )),
                     2
                   )                                                                deviceData,
               2                                                                    deviceDataSource,
               round(sum(CASE vv.elec_root_type WHEN 1 THEN vv.ave ELSE '' END), 2) productionData,
               2                                                                    productionDataSource,
               round(sum(CASE vv.elec_root_type WHEN 2 THEN vv.ave ELSE '' END), 2) managementData,
               2                                                                    managementDataSource,
               round(sum(CASE vv.elec_root_type WHEN 3 THEN vv.ave ELSE '' END), 2) businessData,
               2                                                                    businessDataSource,
               round(sum(CASE vv.elec_root_type WHEN 4 THEN vv.ave ELSE '' END), 2) otherData,
               2                                                                    otherDataSource
        FROM (
                 select m.stationCode,
                        m.countyCode,
                        m.stationName,
                        m.countyName,
                        m.cityCode,
                        m.cityName,
                        (case
                             when date_format(now(), '%m') = 1
                                 then (pq.jan_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 2
                                 then (pq.feb_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 3
                                 then (pq.mar_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 4
                                 then (pq.apr_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 5
                                 then (pq.may_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 6
                                 then (pq.jun_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 7
                                 then (pq.jul_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 8
                                 then (pq.aug_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 9
                                 then (pq.sep_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 10
                                 then (pq.oct_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 11
                                 then (pq.nov_quota_value) / DAYOFMONTH(last_day(curdate()))
                             when date_format(now(), '%m') = 12
                                 then (pq.dec_quota_value) / DAYOFMONTH(last_day(curdate()))
                            end)                     ave,
                        pa.electrotype               elec_type,
                        substr(pa.electrotype, 1, 1) elec_root_type
                 FROM power_quota pq
                          inner join power_ammeterorprotocol pa
                                     on pq.device_id = pa.id
                          inner join meterinfo m
                                     on m.energyMeterCode =
                                        (CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END)
             ) vv
        GROUP BY vv.stationCode
    </insert>
    <insert id="createMeterInfoTowCByAmmeter2ForLn">
        insert into meterinfo_twoc (provinceCode, cityCode, cityName, countyCode, countyName, energyMeterCode,
                                    powerGridEnergyMeterCode,
                                    energyMeterName, status, usageCopy, type, stationCode, stationName, stationLocation,
                                    stationStatus, stationType, energySupplyWay, siteCode)
        SELECT ${parentCode}                                                               provinceCode,
               (select twc_code from department_id d where d.org_id = pap.company)         cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)    cityName,
               pap.FILL_IN_COST_CENTER_ID                                                  countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)         countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
               pap.supplybureauammetercode                                                 powerGridEnergyMeterCode,
               pap.projectname                                                             energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                  `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                           `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                         type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)        stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '' end)        stationName,
               IFNULL(si.address, si.stationname)                                          stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                 stationStatus,
               pap.electrotype                                                             stationType,
               IFNULL(pap.directsupplyflag, 1)                                             energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)        siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
                 left join (select jjt.*
                            from power_station_info_r_jtlte jjt
                                     INNER JOIN (select max(id) mid, stationid
                                                 from power_station_info_r_jtlte
                                                 group by stationid) f
                                                on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id
        UNION
        SELECT ${parentCode}                                                                provinceCode,
               (select twc_code from department_id d where d.org_id = pap.company)          cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)     cityName,
               pap.FILL_IN_COST_CENTER_ID                                                   countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)          countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)  energyMeterCode,
               pap.supplybureauammetercode                                                  powerGridEnergyMeterCode,
               pap.projectname                                                              energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                   `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                            `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                          type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                             stationCode,
               (case when si.resstationname is not null then si.resstationname else '' end) stationName,
               IFNULL(si.address, si.stationname)                                           stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                  stationStatus,
               pap.electrotype                                                              stationType,
               IFNULL(pap.directsupplyflag, 1)                                              energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                             siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype not in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
        UNION
        SELECT ${parentCode}                                                               provinceCode,
               (select twc_code from department_id d where d.org_id = pap.company)         cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)    cityName,
               pap.FILL_IN_COST_CENTER_ID                                                  countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)         countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
               pap.supplybureauammetercode                                                 powerGridEnergyMeterCode,
               pap.projectname                                                             energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                  `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                           `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                         type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)        stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '' end)        stationName,
               IFNULL(si.address, si.stationname)                                          stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                 stationStatus,
               pap.electrotype                                                             stationType,
               IFNULL(pap.directsupplyflag, 1)                                             energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)        siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account_es p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
                 left join (select jjt.*
                            from power_station_info_r_jtlte jjt
                                     INNER JOIN (select max(id) mid, stationid
                                                 from power_station_info_r_jtlte
                                                 group by stationid) f
                                                on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id
        UNION
        SELECT ${parentCode}                                                                provinceCode,
               (select twc_code from department_id d where d.org_id = pap.company)          cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)     cityName,
               pap.FILL_IN_COST_CENTER_ID                                                   countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)          countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)  energyMeterCode,
               pap.supplybureauammetercode                                                  powerGridEnergyMeterCode,
               pap.projectname                                                              energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                   `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                            `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                          type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                             stationCode,
               (case when si.resstationname is not null then si.resstationname else '' end) stationName,
               IFNULL(si.address, si.stationname)                                           stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                  stationStatus,
               pap.electrotype                                                              stationType,
               IFNULL(pap.directsupplyflag, 1)                                              energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                             siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account_es p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype not in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si on si.id = pap.stationcode
    </insert>
    <insert id="createMeterInfoTowCByAmmeter2ForSc">
        insert into meterinfo_twoc (provinceCode, cityCode, cityName, countyCode, countyName, energyMeterCode,
                                    powerGridEnergyMeterCode,
                                    energyMeterName, status, usageCopy, type, stationCode, stationName, stationLocation,
                                    stationStatus, stationType, energySupplyWay, siteCode)
        SELECT ${parentCode}                                                                      provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.FILL_IN_COST_CENTER_ID                                                         countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               pap.supplybureauammetercode                                                        powerGridEnergyMeterCode,
               pap.projectname                                                                    energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '' end)               stationName,
               IFNULL(si.address, si.stationname)                                                 stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               pap.electrotype                                                                    stationType,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
                 left join (select jjt.*
                            from power_station_info_r_jtlte jjt
                                     INNER JOIN (select max(id) mid, stationid
                                                 from power_station_info_r_jtlte
                                                 group by stationid) f
                                                on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id
        UNION
        SELECT ${parentCode}                                                                      provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.FILL_IN_COST_CENTER_ID                                                         countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               pap.supplybureauammetercode                                                        powerGridEnergyMeterCode,
               pap.projectname                                                                    energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   stationCode,
               (case when si.resstationname is not null then si.resstationname else '' end)       stationName,
               IFNULL(si.address, si.stationname)                                                 stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               pap.electrotype                                                                    stationType,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype not in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
        UNION
        SELECT ${parentCode}                                                                      provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.FILL_IN_COST_CENTER_ID                                                         countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               pap.supplybureauammetercode                                                        powerGridEnergyMeterCode,
               pap.projectname                                                                    energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '' end)               stationName,
               IFNULL(si.address, si.stationname)                                                 stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               pap.electrotype                                                                    stationType,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account_es p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si
                           on si.id = pap.stationcode
                 left join (select jjt.*
                            from power_station_info_r_jtlte jjt
                                     INNER JOIN (select max(id) mid, stationid
                                                 from power_station_info_r_jtlte
                                                 group by stationid) f
                                                on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id
        UNION
        SELECT ${parentCode}                                                                      provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.FILL_IN_COST_CENTER_ID                                                         countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               pap.supplybureauammetercode                                                        powerGridEnergyMeterCode,
               pap.projectname                                                                    energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usageCopy`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   stationCode,
               (case when si.resstationname is not null then si.resstationname else '' end)       stationName,
               IFNULL(si.address, si.stationname)                                                 stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               pap.electrotype                                                                    stationType,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   siteCode
        FROM (
                 select pa.*, ma.FILL_IN_COST_CENTER_ID
                 from power_ammeterorprotocol pa,
                      mss_r_billitem_account m,
                      power_account_es p,
                      mss_accountbill ma
                 where pa.id = p.ammeterid
                   and m.account_id = p.pcid
                   and m.bill_id = ma.ID
                   and pa.status = 1
                   and p.status = 3
                   and pa.del_flag = 0
                   and p.del_flag = 0
                   and pa.electrotype not in (1411, 1412, 1421, 1422, 1431, 1432)
                   and substring(ma.BUDGETSETNAME, 1, 4) = 2023
                 group by pa.id
             ) pap
                 left join power_station_info si on si.id = pap.stationcode
    </insert>
    <select id="getProjectNameExist"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        SELECT
            *
        FROM
            power_ammeterorprotocol
        WHERE
            del_flag = 0
            AND status = 1
            <if test="ammeterorprotocol.projectname != null and ammeterorprotocol.projectname != ''">
                AND projectname = #{ammeterorprotocol.projectname}
            </if>
            <if test="ammeterorprotocol.id != null">
                AND id != #{ammeterorprotocol.id}
            </if>
        limit 1
    </select>

    <!-- 统计分析 -->
    <select id="listStatisticalElectricity"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.StatisticalElectricityDTO">
    SELECT company,amount,
           ROUND((amount2 * 100.0) / (amount), 4) AS percentage2,amount2,
           ROUND((amount4 * 100.0) / (amount), 4) AS percentage4,amount4,
           ROUND((amount31 * 100.0) / (amount), 4) AS percentage31,amount31,
           ROUND((amount33 * 100.0) / (amount), 4) AS percentage33,amount33,
           ROUND((amount111 * 100.0) / (amount), 4) AS percentage111,amount111,
           ROUND((amount112 * 100.0) / (amount), 4) AS percentage112,amount112,
           ROUND((amount113 * 100.0) / (amount), 4) AS percentage113,amount113,
           ROUND((amount131 * 100.0) / (amount), 4) AS percentage131,amount131,
           ROUND((amount132 * 100.0) / (amount), 4) AS percentage132,amount132,
           ROUND((amount133 * 100.0) / (amount), 4) AS percentage133,amount133,
           ROUND((amount121 * 100.0) / (amount), 4) AS percentage121,amount121,
		   ROUND((amount122 * 100.0) / (amount), 4) AS percentage122,amount122,
		   ROUND((amount1411 * 100.0) / (amount), 4) AS percentage1411,amount1411,
		   ROUND((amount1412 * 100.0) / (amount), 4) AS percentage1412,amount1412,
		   ROUND((amount1421 * 100.0) / (amount), 4) AS percentage1421,amount1421,
		   ROUND((amount1422 * 100.0) / (amount), 4) AS percentage1422,amount1422,
		   ROUND((amount1431 * 100.0) / (amount), 4) AS percentage1431,amount1431,
		   ROUND((amount1432 * 100.0) / (amount), 4) AS percentage1432,amount1432,
           "全省" companyName
    from(
    select company,sum(amount2+amount4+amount31+amount33+amount111+amount112+amount113+amount131+amount132+amount133+amount121+amount122+amount1411+amount1412+amount1421+amount1422+amount1431+amount1432) amount,amount2,amount4,amount31,amount33,amount111,amount112,amount113,
           amount131,amount132,amount133,amount121,amount122,amount1411,amount1412,amount1421,amount1422,amount1431,amount1432
    from (
    select company,
           sum(electrotype = 2) amount2,
           sum(electrotype = 4) amount4,
           sum(electrotype = 31) amount31,
           sum(electrotype = 33) amount33,
           sum(electrotype = 111) amount111,
           sum(electrotype = 112) amount112,
           sum(electrotype = 113) amount113,
           sum(electrotype = 131) amount131,
           sum(electrotype = 132) amount132,
		   sum(electrotype = 133) amount133,
		   sum(electrotype = 121) amount121,
		   sum(electrotype = 122) amount122,
		   sum(electrotype = 1411) amount1411,
		   sum(electrotype = 1412) amount1412,
		   sum(electrotype = 1421) amount1421,
		   sum(electrotype = 1422) amount1422,
		   sum(electrotype = 1431) amount1431,
		   sum(electrotype = 1432) amount1432
    from power_ammeterorprotocol) s1)s2
    UNION ALL
    SELECT company,amount,
           ROUND((amount2 * 100.0) / (amount), 4) AS percentage2,amount2,
           ROUND((amount4 * 100.0) / (amount), 4) AS percentage4,amount4,
           ROUND((amount31 * 100.0) / (amount), 4) AS percentage31,amount31,
           ROUND((amount33 * 100.0) / (amount), 4) AS percentage33,amount33,
           ROUND((amount111 * 100.0) / (amount), 4) AS percentage111,amount111,
           ROUND((amount112 * 100.0) / (amount), 4) AS percentage112,amount112,
           ROUND((amount113 * 100.0) / (amount), 4) AS percentage113,amount113,
           ROUND((amount131 * 100.0) / (amount), 4) AS percentage131,amount131,
           ROUND((amount132 * 100.0) / (amount), 4) AS percentage132,amount132,
           ROUND((amount133 * 100.0) / (amount), 4) AS percentage133,amount133,
		   ROUND((amount121 * 100.0) / (amount), 4) AS percentage121,amount121,
		   ROUND((amount122 * 100.0) / (amount), 4) AS percentage122,amount122,
		   ROUND((amount1411 * 100.0) / (amount), 4) AS percentage1411,amount1411,
		   ROUND((amount1412 * 100.0) / (amount), 4) AS percentage1412,amount1412,
		   ROUND((amount1421 * 100.0) / (amount), 4) AS percentage1421,amount1421,
		   ROUND((amount1422 * 100.0) / (amount), 4) AS percentage1422,amount1422,
		   ROUND((amount1431 * 100.0) / (amount), 4) AS percentage1431,amount1431,
		   ROUND((amount1432 * 100.0) / (amount), 4) AS percentage1432,amount1432,
           so.org_name companyName
    from (
    select company,sum(amount2+amount4+amount31+amount33+amount111+amount112+amount113+amount131+amount132+amount133+amount121+amount122+amount1411+amount1412+amount1421+amount1422+amount1431+amount1432) amount,amount2,amount4,amount31,amount33,amount111,amount112,amount113,
           amount131,amount132,amount133,amount121,amount122,amount1411,amount1412,amount1421,amount1422,amount1431,amount1432
    from (
    select company,
           sum(electrotype = 2) amount2,
           sum(electrotype = 4) amount4,
           sum(electrotype = 31) amount31,
           sum(electrotype = 33) amount33,
           sum(electrotype = 111) amount111,
           sum(electrotype = 112) amount112,
           sum(electrotype = 113) amount113,
           sum(electrotype = 131) amount131,
           sum(electrotype = 132) amount132,
		   sum(electrotype = 133) amount133,
		   sum(electrotype = 121) amount121,
		   sum(electrotype = 122) amount122,
		   sum(electrotype = 1411) amount1411,
		   sum(electrotype = 1412) amount1412,
		   sum(electrotype = 1421) amount1421,
		   sum(electrotype = 1422) amount1422,
		   sum(electrotype = 1431) amount1431,
		   sum(electrotype = 1432) amount1432
    from power_ammeterorprotocol
    group by company) s1
    group by company)s2
    left join rmp.sys_organizations so on so.id = s2.company
    group by company
    </select>

    <select id="selectByAccountIds" resultMap="AmmeterorprotocolResult">
        select id,protocolname,ammetername
        from power_ammeterorprotocol
        <where>
            category = 1 and isentityammeter = 1
            and id in(select ammeterid from power_account
            where pcid in
            <foreach item="id" collection="accountIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </where>
    </select>
    <delete id="clearCollectMeterByCode">
        delete  from  collectmeter where cityCode = #{collectMeter.cityCode}
                                     and countyCode = #{collectMeter.countyCode}
                                     and stationCode = #{collectMeter.stationCode}
    </delete>
    <insert id="batchInsertAllCollectMeterInfo">
        insert into collectmeter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            collectTime,cityCode, cityName, countyCode, countyName, stationCode,
            stationName, acData, acDataSource, oepgData, oepgDataSource, pvpgData, pvpgDataSource,
            parentStationCode, ccoer, cdcf, energyData, energyDataSource, deviceData,
            deviceDataSource,
            productionData, productionDataSource, managementData, managementDataSource,
            businessData,
            businessDataSource, otherData, otherDataSource
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.collectTime},
                #{item.cityCode},
                #{item.cityName},
                #{item.countyCode},
                #{item.countyName},
                #{item.stationCode},
                #{item.stationName},
                #{item.acData},
                #{item.acDataSource},
                #{item.oepgData},
                #{item.oepgDataSource},
                #{item.pvpgData},
                #{item.pvpgDataSource},
                #{item.parentStationCode},
                #{item.ccoer},
                #{item.cdcf},
                #{item.energyData},
                #{item.energyDataSource},
                #{item.deviceData},
                #{item.deviceDataSource},
                #{item.productionData},
                #{item.productionDataSource},
                #{item.managementData},
                #{item.managementDataSource},
                #{item.businessData},
                #{item.businessDataSource},
                #{item.otherData},
                #{item.otherDataSource}
            </trim>
        </foreach>
    </insert>
    <select id="getAmmeterByCode"
            resultType="com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol">
        SELECT
            id
        FROM
            power_ammeterorprotocol
        WHERE
                ( CASE WHEN category = 1 THEN BINARY ammetername ELSE BINARY protocolname END  )
                = #{ammeterCode}
    </select>
    <select id="getPowerAccountByAmmeter" resultType="java.lang.Long">
        SELECT
            pa.pcid
        FROM power_account pa
        WHERE
            pa.del_flag = '0'
            AND pa.status = 1
            AND pa.ammeterid in
            <foreach collection="ammeterIds" item="ammeterId" open="(" separator="," close=")">
                #{ammeterId}
            </foreach>
    </select>
    <select id="getPowerAccountEsByAmmeter" resultType="com.sccl.modules.business.accountEs.domain.PowerAccountEs">
        SELECT
            pae.*
        FROM power_account_es pae
        WHERE
            pae.del_flag = '0'
            AND pae.status = 1
            AND pae.ammeterid in
            <foreach collection="ammeterIds" item="ammeterId" open="(" separator="," close=")">
                #{ammeterId}
            </foreach>
    </select>

    <select id="selectMssBillByAmmeterIds" resultType="com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill">
        select m.id, m.status
        from mss_accountbill m,
             mss_r_billitem_account r,
             power_account a
        where m.id = r.bill_id
        and a.pcid = r.account_id
        and m.STATUS in (1, 2, -4)
        and a.ammeterid in
        <foreach collection="ammeterIds" item="ammeterId" open="(" separator="," close=")">
            #{ammeterId}
        </foreach>

        union all

        select m.id, m.status
        from mss_accountbill m,
             mss_r_billitem_account r,
             power_account_es a
        where m.id = r.bill_id
        and a.pcid = r.account_id
        and m.STATUS in (1, 2, -4)
        and a.ammeterid in
        <foreach collection="ammeterIds" item="ammeterId" open="(" separator="," close=")">
            #{ammeterId}
        </foreach>
    </select>

    <select id="getCollectMeterByCountyAndWriteoff"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterQueryResult">
        SELECT
            *
        FROM
            (
                SELECT
                    max(start_date) startdate,
                    max(end_date) enddate,
                    this_quantity_of_electricity thisQuantityOfElectricity,
                    26 provinceCode,
                    vv.cityCode,
                    vv.cityName,
                    vv.countyCode,
                    vv.countyName,
                    vv.stationCode,
                    vv.stationName,
                    county_name,
                    mss_data acData,
                    223 acDataSource,
                    0 oepgData,
                    2 oepgDataSource,
                    0 pvpgData,
                    2 pvpgDataSource,
                    '' parentStationCode,
                    '' ccoer,
                    '' cdcf,
                    mss_data energyData,
                    223 energyDataSource,
                    ROUND(mss_data*0.95, 2) deviceData,
                    2 deviceDataSource,
                    mss_data productionData,
                    2 productionDataSource,
                    0 managementData,
                    2 managementDataSource,
                    0 businessData,
                    2 businessDataSource,
                    0 otherData,
                    2 otherDataSource,
                    0 del_flag,
                    1 syncFlag
                FROM
                    (
                        SELECT
                            mdb.city_Code cityCode,
                            mdb.city_Name cityName,
                            mdb.county_Code countyCode,
                            mdb.county_name countyName,
                            mdb.station_Code stationCode,
                            mdb.station_Name stationName,
                            round(this_quantity_of_electricity / sum(DATEDIFF(electricity_end_date, electricity_start_date) + 1), 2) AS mss_data,
                            writeoff_instance_code,
                            wdd.county_name,
                            wdd.energy_meter_code,
                            min(electricity_start_date) start_date,
                            max(electricity_end_date) end_date,
                            round(this_quantity_of_electricity, 2) this_quantity_of_electricity,
                            sum(DATEDIFF(electricity_end_date, electricity_start_date) + 1) AS days
                        FROM
                            (
                                SELECT
                                    gg.*
                                FROM
                                    (SELECT max(msg_id) mmsgid, billid FROM meter_info_db_bases wdd GROUP BY billid) dd
                                    INNER JOIN meter_info_db_bases gg ON gg.msg_id = dd.mmsgid
                                AND dd.billid = gg.billid) mdb
                            INNER JOIN (
                                SELECT
                                    gg.*
                                FROM
                                    (SELECT max(msg_id) mmsgid, billid FROM writeoffinfodb wdd GROUP BY billid) dd
                                    INNER JOIN writeoffinfodb gg ON gg.msg_id = dd.mmsgid
                                AND dd.billid = gg.billid) wdd ON mdb.billid = wdd.billid
                            AND mdb.energy_meter_code = wdd.energy_meter_code
                        WHERE
                            wdd.county_name = #{periodNumber}
                            AND mdb.station_code in ( SELECT station_code FROM meter_info_db_bases WHERE billid IN (SELECT ID AS id FROM mss_accountbill WHERE WRITEOFF_INSTANCE_CODE IN (#{writeoffInstanceCode})) )
                        GROUP BY
                            energy_meter_code,
                            writeoff_instance_code) vv
                GROUP BY
                    vv.stationCode, vv.energy_meter_code) bb
        ORDER BY
            bb.cityCode,
            bb.countyCode
    </select>

    <!-- 查询智能电表数据 -->
    <select id="getSmartMeterData" resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        SELECT
            DATE_FORMAT(t.collectTime, '%Y%m%d') AS collectTime,
            26 AS provinceCode,
            COALESCE(jt.cityCode, '') AS cityCode,
            COALESCE(jt.cityName, '') AS cityName,
            COALESCE(jt.countyCode, '') AS countyCode,
            COALESCE(jt.countyName, '') AS countyName,
            t.stationId AS stationCode,
            COALESCE(jt.stationName, '') AS stationName,
            ROUND(t.totalMeterEnergy, 2) AS acData,
            223 AS acDataSource,
            0 AS oepgData,
            2 AS oepgDataSource,
            0 AS pvpgData,
            2 AS pvpgDataSource,
            '' AS parentStationCode,
            '' AS ccoer,
            '' AS cdcf,
            ROUND(t.totalMeterEnergy, 2) AS energyData,
            223 AS energyDataSource,
            ROUND(t.totalMeterEnergy * 0.95, 2) AS deviceData,
            2 AS deviceDataSource,
            ROUND(t.totalMeterEnergy, 2) AS productionData,
            2 AS productionDataSource,
            0 AS managementData,
            2 AS managementDataSource,
            0 AS businessData,
            2 AS businessDataSource,
            0 AS otherData,
            2 AS otherDataSource
        FROM (
            SELECT
                collectTime,
                stationId,
                SUM(CAST(meterEnery AS DECIMAL(18,2))) AS totalMeterEnergy
            FROM zndb_device_all
            WHERE DATE(collectTime) = #{collectTime}
              AND meterEnery IS NOT NULL
              AND meterEnery != ''
              AND eneryType in (1, 6)
            GROUP BY collectTime, stationId
        ) t
        LEFT JOIN (
            SELECT
                m1.stationCode,
                m1.cityCode,
                m1.cityName,
                m1.countyCode,
                m1.countyName,
                m1.stationName
            FROM meterinfo_all_jt m1
            INNER JOIN (
                SELECT stationCode, MIN(id) as min_id
                FROM meterinfo_all_jt
                WHERE stationCode IS NOT NULL
                GROUP BY stationCode
            ) m2 ON m1.stationCode = m2.stationCode AND m1.id = m2.min_id
        ) jt ON t.stationId = jt.stationCode
        WHERE countyCode is not null and t.totalMeterEnergy >= 0;
    </select>

    <!-- 根据countyCode和stationCode查询collectmeter表中的记录 -->
    <select id="getCollectMeterByCountyAndStation" resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        SELECT
            id,
            collectTime,
            provinceCode,
            cityCode,
            cityName,
            countyCode,
            countyName,
            stationCode,
            stationName,
            acData,
            acDataSource,
            oepgData,
            oepgDataSource,
            pvpgData,
            pvpgDataSource,
            parentStationCode,
            ccoer,
            cdcf,
            energyData,
            energyDataSource,
            deviceData,
            deviceDataSource,
            productionData,
            productionDataSource,
            managementData,
            managementDataSource,
            businessData,
            businessDataSource,
            otherData,
            otherDataSource,
            del_flag,
            syncFlag,
            failMag
        FROM collectmeter
        WHERE countyCode = #{countyCode}
          AND stationCode = #{stationCode}
    </select>

    <!-- 更新collectmeter表记录 -->
    <update id="updateCollectMeter">
        UPDATE collectmeter
        SET
            acData = #{acData},
            oepgData = #{oepgData},
            pvpgData = #{pvpgData},
            energyData = #{energyData},
            deviceData = #{deviceData},
            productionData = #{productionData},
            managementData = #{managementData},
            businessData = #{businessData},
            otherData = #{otherData},
            syncFlag = #{syncFlag}
        WHERE countyCode = #{countyCode}
          AND stationCode = #{stationCode}
    </update>

    <!-- 插入collectmeter表记录 -->
    <insert id="insertCollectMeter">
        INSERT INTO collectmeter (
            collectTime,
            provinceCode,
            cityCode,
            cityName,
            countyCode,
            countyName,
            stationCode,
            stationName,
            acData,
            acDataSource,
            oepgData,
            oepgDataSource,
            pvpgData,
            pvpgDataSource,
            parentStationCode,
            ccoer,
            cdcf,
            energyData,
            energyDataSource,
            deviceData,
            deviceDataSource,
            productionData,
            productionDataSource,
            managementData,
            managementDataSource,
            businessData,
            businessDataSource,
            otherData,
            otherDataSource,
            del_flag,
            syncFlag
        ) VALUES (
            #{collectTime},
            #{provinceCode},
            #{cityCode},
            #{cityName},
            #{countyCode},
            #{countyName},
            #{stationCode},
            #{stationName},
            #{acData},
            #{acDataSource},
            #{oepgData},
            #{oepgDataSource},
            #{pvpgData},
            #{pvpgDataSource},
            #{parentStationCode},
            #{ccoer},
            #{cdcf},
            #{energyData},
            #{energyDataSource},
            #{deviceData},
            #{deviceDataSource},
            #{productionData},
            #{productionDataSource},
            #{managementData},
            #{managementDataSource},
            #{businessData},
            #{businessDataSource},
            #{otherData},
            #{otherDataSource},
            #{del_flag},
            #{syncFlag}
        )
    </insert>

    <!-- 根据期号查询collectmeter_v2_期号表的全部数据 -->
    <select id="getAllCollectMeterByPeriod"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterQueryResult">
        SELECT
            start_date AS startdate,
            end_date AS enddate,
            total_quantity_of_electricity AS thisQuantityOfElectricity,
            province_code AS provinceCode,
            city_code AS cityCode,
            city_name AS cityName,
            county_code AS countyCode,
            county_name AS countyName,
            station_code AS stationCode,
            station_name AS stationName,
            county_name AS county_name,
            ac_data AS acData,
            ac_data_source AS acDataSource,
            oepg_data AS oepgData,
            oepg_data_source AS oepgDataSource,
            pvpg_data AS pvpgData,
            pvpg_data_source AS pvpgDataSource,
            parent_station_code AS parentStationCode,
            ccoer,
            cdcf,
            energy_data AS energyData,
            energy_data_source AS energyDataSource,
            device_data AS deviceData,
            device_data_source AS deviceDataSource,
            production_data AS productionData,
            production_data_source AS productionDataSource,
            management_data AS managementData,
            management_data_source AS managementDataSource,
            business_data AS businessData,
            business_data_source AS businessDataSource,
            other_data AS otherData,
            other_data_source AS otherDataSource,
            del_flag,
            sync_flag AS syncFlag
        FROM collectmeter_v2_${periodNumber}
        WHERE del_flag = 0
        ORDER BY city_code, county_code, station_code
    </select>
</mapper>
