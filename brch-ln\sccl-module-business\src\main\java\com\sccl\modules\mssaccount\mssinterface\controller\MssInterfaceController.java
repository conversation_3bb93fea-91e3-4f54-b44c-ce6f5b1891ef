package com.sccl.modules.mssaccount.mssinterface.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.service.IMssAccountbillService;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import com.sccl.modules.mssaccount.mssinterface.service.IMssInterfaceService;
import com.sccl.modules.mssaccount.mssinterface.service.MssInterfaceServiceImpl;
import com.sccl.modules.mssaccount.mssinterface.service.MssJsonClient;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

/**
 * MSS财务接口 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@RestController
@Slf4j
@RequestMapping("/mssaccount/mssInterface")
public class MssInterfaceController extends BaseController {
    @Autowired
    MssInterfaceServiceImpl mssInterfaceServiceImpl;
    @Autowired
    IMssAccountbillService mssAccountbillService;
    private String prefix = "mssaccount/mssInterface";
    @Autowired
    private IMssInterfaceService mssInterfaceService;
    @Value("${powerStationInfoAveDayElec.maxmonth}")
    private Integer maxMonth;
    @Value("${sccl.deployTo}")
    private String deployTo;
    public static boolean doSyncSpanInfoFalg = true;
    public static boolean doSyncSpanInfoFalg3 = true;

    public static void main(String[] args) {
        long l = Long.parseLong("4456597797763969024");

    }

    @RequiresPermissions("mssaccount:mssInterface:view")
    @GetMapping()
    public String mssInterface() {
        return prefix + "/mssInterface";
    }

    /**
     * 查询MSS财务接口列表
     * @param mssInterface 报账单信息
     * @return 结果
     */
    @RequiresPermissions("mssaccount:mssInterface:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssInterface mssInterface) {
        startPage();
        List<MssInterface> list = mssInterfaceService.selectList(mssInterface);
        return getDataTable(list);
    }

    /**
     * 新增MSS财务接口
     * @return 结果
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存MSS财务接口
     * @param mssInterface 报账单信息
     * @return 结果
     */
    @RequiresPermissions("mssaccount:mssInterface:add")
    //@Log(title = "MSS财务接口", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssInterface mssInterface) {
        return toAjax(mssInterfaceService.insert(mssInterface));
    }

    /**
     * 修改MSS财务接口
     * @param miid 报账单id
     * @return 结果
     */
    @GetMapping("/edit/{miid}")
    public AjaxResult edit(@PathVariable("miid") Long miid) {
        MssInterface mssInterface = mssInterfaceService.get(miid);

        Object object = JSONObject.toJSON(mssInterface);

        return this.success(object);
    }

    /**
     * 修改保存MSS财务接口
     * @param mssInterface 报账单信息
     * @return 结果
     */
    @RequiresPermissions("mssaccount:mssInterface:edit")
    //@Log(title = "MSS财务接口", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssInterface mssInterface) {
        return toAjax(mssInterfaceService.update(mssInterface));
    }

    /**
     * 删除MSS财务接口
     * @param ids 报账单id列表
     * @return 结果
     */
    @RequiresPermissions("mssaccount:mssInterface:remove")
    //@Log(title = "MSS财务接口", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
//		return toAjax(mssInterfaceService.deleteByIds(ConvertFormat.toStrArray(ids)));
        return toAjax(mssInterfaceService.deleteByIdsDB(Convert.toStrArray(ids)));
    }

    /**
     * 查看MSS财务接口
     * @param miid 报账单id
     * @return 结果
     */
//    @RequiresPermissions("mssaccount:mssInterface:view")
    @GetMapping("/view/{miid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("miid") Long miid) {
        MssInterface mssInterface = mssInterfaceService.get(miid);

        Object object = JSONObject.toJSON(mssInterface);

        return this.success(object);
    }

    /**
     * 流程提交 MSS财务接口
     * @param id 报账单id
     * @throws Exception 异常
     */
    //@Log(title = "MSS财务接口", action = BusinessType.INSERT)
    @GetMapping("/sendToMss/{id}")
    @ResponseBody
    public void sendToMss(@PathVariable("id") Long id) throws Exception {
        mssInterfaceService.sendToMss(id);
    }

    /**
     * 批量提交报账单到财辅
     * @param ids 报账单id列表
     * @return 结果
     * @throws Exception 异常
     */
    //@Log(title = "MSS财务接口", action = BusinessType.INSERT)
    @PostMapping("/sendToMssPro")
    @ResponseBody
    public String sendToMssPro(@RequestBody List<Long> ids) throws Exception {
        ids.forEach(billid -> {
            try {
                mssInterfaceService.sendToMss(billid);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        return "当前批量报账单推送财辅执行完毕";
    }


    /**
     * 查询报账轮询执行情况
     *
     * @param billid 报账单id
     * @param executeStatus 轮询状态
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/queryStatuswaitGeneration")
    @ResponseBody
    public String queryStatuswaitGeneration(@RequestParam(required = false, value = "billId") Long billid, @RequestParam(required = false, value = "executeStatus") Integer executeStatus) throws Exception {
        StringJoiner result = new StringJoiner("||\n", "status=-4报账单能耗系统轮询情况:\n", "||");
        AtomicInteger count = new AtomicInteger();
        log.info("根据输入参数查询执行不同查询路径");
        if (billid != null && executeStatus != null) {
            return "不支持的查询参数,billid 和 executeStatus只能选择一个";
        }
        if (billid != null) {
            log.info("查询单条报账单");
            BillExecuteState billExecuteState = MssInterfaceServiceImpl.executeMap.get(billid);
            count.addAndGet(1);
            result.add(billExecuteState != null ? String.format("%d->%s", billid, billExecuteState.getDesc()) : String.format("未查询到报账单id=%d的轮询情况", billid));

            return result + String.format("\ncount:%d", count.get());
        }
        if (executeStatus != null) {
            log.info("查询报账单轮询状态");
            MssInterfaceServiceImpl.executeMap.forEach((k, v) -> {
                if (v.getState() == executeStatus) {
                    result.add(String.format("%d->%s", k, v.getDesc()));
                    count.addAndGet(1);
                }
            });
            return result + String.format("\ncount:%d", count.get());
        }

        log.info("查询全量执行信息");
        MssInterfaceServiceImpl.executeMap.forEach((k, v) -> {
            result.add(String.format("%d->%s", k, v.getDesc()));
            count.addAndGet(1);
        });
        return result + String.format("\ncount:%d", count.get());
    }

    /**
     * 查询报账轮询 status=3 执行情况
     *
     * @param billid 报账单id
     * @param executeStatus 轮询状态
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/queryStatuswaitGenerationPro")
    @ResponseBody
    public String queryStatuswaitGenerationPro(@RequestParam(required = false, value = "billId") Long billid, @RequestParam(required = false, value = "executeStatus") Integer executeStatus) throws Exception {
        StringJoiner result = new StringJoiner("||\n", "status=3报账单能耗系统轮询情况:\n", "||");
        AtomicInteger count = new AtomicInteger();
        log.info("根据输入参数查询执行不同查询路径");
        if (billid != null && executeStatus != null) {
            return "不支持的查询参数,billid 和 executeStatus只能选择一个";
        }
        if (billid != null) {
            log.info("查询单条报账单");
            BillExecuteState billExecuteState = MssInterfaceServiceImpl.executeMap3.get(billid);
            count.addAndGet(1);
            result.add(billExecuteState != null ? String.format("%d->%s", billid, billExecuteState.getDesc()) : String.format("未查询到报账单id=%d的轮询情况", billid));

            return result + String.format("\ncount:%d", count.get());
        }
        if (executeStatus != null) {
            log.info("查询报账单轮询状态");
            MssInterfaceServiceImpl.executeMap3.forEach((k, v) -> {
                if (v.getState() == executeStatus) {
                    result.add(String.format("%d->%s", k, v.getDesc()));
                    count.addAndGet(1);
                }
            });
            return result + String.format("\ncount:%d", count.get());
        }

        log.info("查询全量执行信息");
        MssInterfaceServiceImpl.executeMap3.forEach((k, v) -> {
            result.add(String.format("%d->%s", k, v.getDesc()));
            count.addAndGet(1);
        });
        return result + String.format("\ncount:%d", count.get());
    }

    /**
     * 轮询map更新
     *
     * @param billid 报账单id
     * @param executeStatus 轮询状态
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/updateStatuswaitGeneration")
    @ResponseBody
    public String updateStatuswaitGeneration(@RequestParam(required = false, value = "billId") Long billid, @RequestParam(required = false, value = "executeStatus") Integer executeStatus) throws Exception {

        if (billid == null || executeStatus == null) {
            return "不支持的请求参数,billid 和 executeStatus 必须全部选择";
        }
        BillExecuteState build = BillExecuteState.build(executeStatus);
        MssInterfaceServiceImpl.executeMap.put(billid, build);

        return String.format("轮询map billid=%d 被设置为%s", billid, build.getDesc());
    }

    /**
     * 轮询map更新
     *
     * @param billid 报账单id
     * @param executeStatus 轮询状态
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/updateStatuswaitGenerationPro")
    @ResponseBody
    public String updateStatuswaitGenerationPro(@RequestParam(required = false, value = "billId") Long billid, @RequestParam(required = false, value = "executeStatus") Integer executeStatus) throws Exception {

        if (billid == null || executeStatus == null) {
            return "不支持的请求参数,billid 和 executeStatus 必须全部选择";
        }
        BillExecuteState build = BillExecuteState.build(executeStatus);
        MssInterfaceServiceImpl.executeMap3.put(billid, build);

        return String.format("轮询map billid=%d 被设置为%s", billid, build.getDesc());
    }

    /**
     * 轮询map删除
     */
    @GetMapping("/delStatuswaitGeneration")
    @ResponseBody
    public String delStatuswaitGeneration(@RequestParam(value = "billId") Long billid) throws Exception {
        if (billid != -1L) {
            BillExecuteState remove = MssInterfaceServiceImpl.executeMap.remove(billid);
            if (remove != null) {
                return String.format("billid=%d 的轮询状态:%s已删除", billid, remove.getDesc());
            }
            return String.format("轮询map不存在对应的billid= %d的轮询状态", billid);
        } else {
            MssInterfaceServiceImpl.executeMap.clear();
            return "轮询mpa已置空";
        }

    }

    /**
     * 轮询map删除
     *
     */
    @GetMapping("/delStatuswaitGenerationPro")
    @ResponseBody
    public String delStatuswaitGenerationPro(@RequestParam(value = "billId") Long billid) throws Exception {
        if (billid != -1L) {
            BillExecuteState remove = MssInterfaceServiceImpl.executeMap3.remove(billid);
            if (remove != null) {
                return String.format("billid=%d 的轮询状态:%s已删除", billid, remove.getDesc());
            }
            return String.format("轮询map不存在对应的billid= %d的轮询状态", billid);
        } else {
            MssInterfaceServiceImpl.executeMap3.clear();
            return "轮询mpa已置空";
        }

    }

    /**
     * 更改报账单轮询分支
     *
     * @param doSyncSpanInfoFalg 轮询分支
     * @return 状态
     */
    @GetMapping("/updatedoSyncSpanInfoFalg")
    @ResponseBody
    public String updatedoSyncSpanInfoFalg(@RequestParam(value = "doSyncSpanInfoFalg") boolean doSyncSpanInfoFalg) {
        MssInterfaceController.doSyncSpanInfoFalg = doSyncSpanInfoFalg;
        boolean flag = MssInterfaceController.doSyncSpanInfoFalg;
        return String.format("当前轮询报账单为:%s", flag ? "传统单线程轮询" : "异步多线程轮询");
    }

    /**
     * 更改采集传送分支
     *
     * @param CollectFalg 采集传送分支
     * @return 状态
     */
    @GetMapping("/updateCollectFalg")
    @ResponseBody
    public String updateCollectFalg(@RequestParam(value = "CollectFalg") boolean CollectFalg) {
        MssJsonClient.collectFlag = CollectFalg;
        boolean flag = MssJsonClient.collectFlag;
        return String.format("当前传送采集数据:%s", flag ? "传送集团" : "不传送集团，只写DB");
    }

    /**
     * 测试
     *
     * @param id 报账单id
     * @throws Exception 抛出异常
     */
    @GetMapping("/sendToMss2/{id}")
    @ResponseBody
    public void sendToMss2(@PathVariable("id") Long id) throws Exception {
        mssInterfaceService.sendToMss2(id);
    }

    /**
     * 轮询任务
     */
    //@Log(title = "MSS财务接口 轮询任务 10分钟一次", action = BusinessType.INSERT)
    @GetMapping("/doSyncSpanInfo")
    public void doSyncSpanInfo() {
//        放前面
//        mssInterfaceService.sendToMssByError();// 送财辅失败的 重新送一次
        /*        if (doSyncSpanInfoFalg) {*/
        mssInterfaceService.getStausBybills();// 轮询报账单 staus=-4
/*        } else {
            mssInterfaceService.getStausBybillsPro();
        }*/

    }

    /**
     * 轮询任务
     */
    @GetMapping("/doSyncSpanInfo3")
    public void doSyncSpanInfo3() {
//        放前面
//        mssInterfaceService.sendToMssByError();// 送财辅失败的 重新送一次
        if (doSyncSpanInfoFalg3) {
            mssInterfaceService.getStausBybills3();// 轮询报账单 staus=3
        } else {
            mssInterfaceService.getStausBybillsPro3();
        }

    }

    /**
     * 轮询任务
     */
    //@Log(title = "MSS财务接口 轮询任务 10分钟一次", action = BusinessType.INSERT)
    @GetMapping("/getStausBybillsSap")
    public void getStausBybillsSap() {
        mssInterfaceService.getStausBybillsSap(); // 轮询是否生成凭证
    }

    @GetMapping("/getFileorgCode")
    @ResponseBody
    public AjaxResult getFileorgCode() {
        List<ViewOrgSapCostCenter> list = new ArrayList<>();
        try {
            User user = ShiroUtils.getUser();
            if (user != null) {
                String hrLoginId = user.getHrLoginId();
                list = mssInterfaceService.getFileorgCode(hrLoginId);
//                list = mssInterfaceService.getFileorgCode("21021493");
            }
        } catch (Exception e) {
            return this.error("系统异常，请联系管理员" + e.getMessage());
        }
        Object object = JSONObject.toJSON(list);
        return this.success(object);
    }

    //根据 财辅报账单 instanceCode 获取 sap凭证
    @PostMapping("/getSapMesByInstanceCode")
    @ResponseBody
    public AjaxResult getSapMesByInstanceCode(String instanceCode) {
        Object object = null;
        try {
            Map<String, Object> map = mssInterfaceService.getSapMesByInstanceCode(instanceCode);
            object = JSONObject.toJSON(map);
        } catch (Exception e) {
            return this.error("未查询到SAP凭证，系统异常，请联系管理员" + e.getMessage());
        }
        return this.success(object);
    }

    @GetMapping("/getbillStatus/{id}")
    @ResponseBody
    public AjaxResult getbillStatus(@PathVariable("id") String id) throws Exception {
        MssAccountbill mssAccountbill = mssAccountbillService.get(Long.valueOf(id));

        if (mssAccountbill != null && -3 == mssAccountbill.getStatus()) {
            Map<String, Object> map = mssInterfaceServiceImpl.handleMssInterface(mssAccountbill);
            return this.success(JSONObject.toJSON(map));
        } else {
            return this.success("");
        }
    }


    @GetMapping("/transPowerData/{id}")
    @ResponseBody
    public AjaxResult transPowerData(@PathVariable("id") String id) {
        mssInterfaceServiceImpl.transPowerData(Long.valueOf(id));
        return this.success("报账电表信息上传集团开始");
    }

    @GetMapping("/getStatus/{id}")
    @ResponseBody
    public AjaxResult getStatus(@PathVariable("id") String id) throws Exception {
        String name = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
        if ("sc".equals(deployTo)) {
            name = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
        } else if ("ln".equals(deployTo)) {
            name = "NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
        } else {
            name = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
        }
        String sendXML = mssInterfaceServiceImpl.sendwriteoffInstanceCodeXML(null, id, name);
        String resXML = mssInterfaceServiceImpl.doMssHttp(sendXML);
        System.out.println("getStatus :" + resXML);
        Map<String, Object> map = mssInterfaceServiceImpl.praseOP_GainWriteoffInstStatus(resXML);
        return this.success(JSONObject.toJSON(map));
    }

    // 根据 报账单号 生成送xml
    @GetMapping("/getbillXml/{id}")
    @ResponseBody
    public AjaxResult getbillXml(@PathVariable("id") Long id) throws Exception {
        MssAccountbill bill = mssAccountbillService.getByid(id);
        String billXML = mssInterfaceServiceImpl.sendBillXML(bill);
        System.out.println(billXML);
        return this.success(JSONObject.toJSON(billXML));
    }

    // 判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”下所有的细类，
    // 即“1411,1412,1421,1422,1431,1432”时
    @GetMapping("/countAmmeterTypeBybillId/{id}")
    @ResponseBody
    public int countAmmeterTypeBybillId(@PathVariable("id") Long id) throws Exception {
        return mssAccountbillService.countAmmeterTypeBybillId(id);
    }

    // 判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”下所有的细类，
    // 即“1411,1412,1421,1422,1431,1432”时
    @GetMapping("/countLteStationBybillId/{id}")
    @ResponseBody
    public int countLteStationBybillId(@PathVariable("id") Long id) throws Exception {
        return mssAccountbillService.countLteStationBybillId(id);
    }

    //  集团 同步电表基础数据
    @GetMapping("/syncWriteoffInfos/{id}")
    @ResponseBody
    public AjaxResult syncWriteoffInfos(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.selectWriteoffDetailInfo(id, "2"));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //  集团 同步失败报账单
    @GetMapping("/syncfailbiills")
    @ResponseBody
    public AjaxResult syncfailbiill(@RequestParam("billids") String ids) throws Exception {
        try {
            String[] idlist = ids.split("-");
            Stream.of(idlist).forEach(k -> {
                try {
                    syncEnergyMeterInfosBybill(Long.valueOf(k));
                    mssInterfaceServiceImpl.selectWriteoffDetailInfo(Long.valueOf(k), "2");
                } catch (Exception e) {
                    log.error("操作异常", e);
                }
            });
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
        return AjaxResult.success("执行完毕");
    }

    /**
     * 集团 同步抄表数据
     *
     * @param id id
     * @return 结果
     */
    @GetMapping("/syncCopyMeterInfors/{id}")
    @ResponseBody
    public AjaxResult syncWriteoffInfos2(@PathVariable("id") Long id) {
        try {
            return this.success(mssInterfaceServiceImpl.selectWriteoffDetailInfo2(id + "/create"));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 双碳电表用电数据生成
     *
     * @param id id
     * @return 结果
     */
    @GetMapping("/syncCopyMeterInforTwoC/{id}")
    @ResponseBody
    public AjaxResult syncCopyMeterInforTwoC(@PathVariable("id") Long id, @RequestParam("checkenergemetercode") String checkenergemetercode, @RequestParam("checkFlag") boolean checkFlag) {
        try {
            return this.success(mssInterfaceServiceImpl.syncCopyMeterInforTwoC(id + "/create", checkenergemetercode, checkFlag));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 双碳用电数据按账期生成
     *
     */
    @GetMapping("/syncCopyMeterInforTwoCBitch/{time}")
    @ResponseBody
    public AjaxResult syncCopyMeterInforTwoCByMonth(@PathVariable("time") String time, @RequestParam("checkenergemetercode") String checkenergemetercode, @RequestParam("checkFlag") boolean checkFlag) {
        try {
            return this.success(mssInterfaceServiceImpl.syncCopyMeterInforTwoCByMonth(time + "/create", checkenergemetercode, checkFlag));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 双碳电表基础信息按账期生成
     *
     * @return 结果
     */
    @GetMapping("/WriteMeterInfo/{budget}")
    @ResponseBody
    public AjaxResult WriteMeterInfo(@PathVariable("budget") String budget) {
        try {
            return this.success(mssInterfaceServiceImpl.WriteMeterInfo(budget));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    @GetMapping("/testUpdateEqu/{id}")
    @ResponseBody
    public AjaxResult testUpdateEqu(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.selectmeterEquipmentInfors(id + "/delete"));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }


    //  集团 同步电表基础数据 作废
    @GetMapping("/syncWriteoffInfosKill/{id}")
    @ResponseBody
    public AjaxResult syncWriteoffInfosKill(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.selectWriteoffDetailInfo(id, "3"));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    @GetMapping("/syncWriteoffInfosKillAddPcids/{billId}")
    @ResponseBody
    public AjaxResult syncWriteoffInfosKillAddPcids(
            @PathVariable("billId") Long id,
            @RequestParam(value = "pcids") String pcids
    ) throws Exception {
        try {
            if (StringUtils.isBlank(pcids)) {
                throw new RuntimeException("请指定pcids");
            }

            List<String> pcidList = (List<String>) Arrays.asList(pcids.split("-"));
            return this.success(mssInterfaceServiceImpl.selectWriteoffDetailInfoAddPcids(id, "3", pcidList));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //集团 同步计量设备信息模板
    @GetMapping("/syncmeterEquipmentInfors")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInfors() throws Exception {
        try {
            int month = LocalDate.now().getMonthValue();
            return this.success(mssInterfaceServiceImpl.selectmeterEquipmentInfors(month));
        } catch (Exception e) {
            log.error("集团 同步计量设备信息模板异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //集团 同步省能耗管理平台中抄表数据
    @GetMapping("/syncCopyMeterInfors")
    @ResponseBody
    public AjaxResult syncCopyMeterInfors() throws Exception {
        try {
            int month = LocalDate.now().getMonthValue();
            return this.success(mssInterfaceServiceImpl.selectCopyMeterInfors(month));
        } catch (Exception e) {
            log.error("集团 同步省能耗管理平台中抄表数据异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //集团 同步智能采集的电量数据
    @GetMapping("/syncCollectMeterInfors/{pcid}")
    @ResponseBody
    public AjaxResult syncCollectMeterInfors(@PathVariable Long pcid) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.selectCollectMeterInfors(pcid));
        } catch (Exception e) {
            log.error("集团 同步智能采集的电量数据异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //集团 同步智能采集的电量数据 支持指定 报账单id和同步时间
    @GetMapping("/syncCollectMeterInfors/{pcid}/{collectTime}")
    @ResponseBody
    public AjaxResult syncCollectMeterInfors2(@PathVariable(value = "pcid") Long pcid, @PathVariable(value = "collectTime") String collectTime) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.selectCollectMeterInfors2(pcid, collectTime));
        } catch (Exception e) {
            log.error("集团 同步智能采集的电量数据 支持指定 报账单id和同步时间异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //集团 同步智能采集的电量数据 支持 批量同步时间
    @PostMapping("/syncCollectMeterInfors/{pcid}/bitchTime")
    @ResponseBody
    public AjaxResult syncCollectMeterInfors3(@PathVariable(required = false, value = "pcid") Long pcid, @RequestBody List<String> collectTimes) throws Exception {
        try {
            CompletableFuture.runAsync(() -> {
                collectTimes.forEach(time -> this.success(mssInterfaceServiceImpl.selectCollectMeterInfors2(pcid, time)));
            });
            return AjaxResult.success("批量采集同步异步任务开启");
        } catch (Exception e) {
            log.error("批量同步异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步智能电表数据（支持单个日期或批量日期）
     * @param request 请求参数，collectTime支持单个日期(2024-05-26)或批量日期(20250612-20250613-20250614)
     * @return 同步结果
     */
    @PostMapping("/syncSmartMeterData")
    public AjaxResult syncSmartMeterData(@RequestBody SyncSmartMeterDataRequest request) {
        try {
            log.info("开始同步智能电表数据，采集时间：{}，是否更新collectmeter表：{}",
                request.getCollectTime(), request.getUpdateCollectMeter());
            SyncSmartMeterDataResponse response = mssInterfaceServiceImpl.syncSmartMeterData(
                request.getCollectTime(), request.getUpdateCollectMeter());

            if ("success".equals(response.getStatus())) {
                return AjaxResult.success(response.getMessage(), response);
            } else {
                return AjaxResult.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("同步智能电表数据异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 根据期号和报账单同步电量数据
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/syncCollectMeterByCountyAndWriteoff")
    public AjaxResult syncCollectMeterByCountyAndWriteoff(@RequestBody SyncCollectMeterRequest request) {
        try {
            if (CollUtil.isNotEmpty(request.getWriteoffInstanceCode())) {
                for (String code : request.getWriteoffInstanceCode()) {
                    String result = mssInterfaceServiceImpl.syncCollectMeterByCountyAndWriteoff(request.getPeriodNumber(), code);
                    log.info("批量同步结果: {}", result);
                }
            }
            return AjaxResult.success("同步完成");
        } catch (Exception e) {
            log.error("批量同步异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 通过报账表反查meter_info_db_bases缺失的数据
     * @param countyName 期号(如: 202506)
     * @return 缺失的数据列表
     */
    @GetMapping("/checkMissingMeterInfoData")
    public AjaxResult checkMissingMeterInfoData(@RequestParam String countyName) {
        try {
            List<Map<String, Object>> missingData = mssInterfaceServiceImpl.checkMissingMeterInfoData(countyName);
            return AjaxResult.success("查询完成", missingData);
        } catch (Exception e) {
            log.error("查询缺失数据过程中发生异常: {}", e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 通过报账表反查meter_info_db_bases缺失的站点数据统计
     * @param countyName 期号(如: 202506)
     * @return 缺失的站点统计信息
     */
    @GetMapping("/checkMissingStationData")
    public AjaxResult checkMissingStationData(@RequestParam String countyName) {
        try {
            Map<String, Object> result = mssInterfaceServiceImpl.checkMissingStationData(countyName);
            return AjaxResult.success("查询完成", result);
        } catch (Exception e) {
            log.error("查询缺失站点数据过程中发生异常: {}", e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    //集团 同步智能采集的电量数据 支持 批量同步时间
    @GetMapping("/syncCollectMeterInfors/{pcid}/bitchTime")
    @ResponseBody
    public AjaxResult syncCollectMeterInfors4(@PathVariable(required = false, value = "pcid") Long pcid, @RequestParam(value = "time") String time) throws Exception {
        try {
            List<String> collectTimes = Arrays.asList(time.split("-"));
            collectTimes.forEach(Synctime -> this.success(mssInterfaceServiceImpl.selectCollectMeterInfors2(pcid, Synctime)));
            return AjaxResult.success("批量同步成功");
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    @GetMapping("/syncCollectMeterInfors/bitchTimePro")
    @ResponseBody
    public AjaxResult syncCollectMeterInforsPro(@RequestParam(value = "time") String time, @RequestParam(value = "budget") String budget) throws Exception {
        try {
            List<String> collectTimes = Arrays.asList(time.split("-"));
            collectTimes.forEach(Synctime -> this.success(mssInterfaceServiceImpl.selectCollectMeterInfors2Pro(-1L, Synctime, budget)));
            return AjaxResult.success("批量同步成功");
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 修复超出报账的电量
     * 根据业务电量调整采集数据，业务电量包含90天外数据，需要在原始电量中进行对冲调整
     *
     * @param request 请求参数
     * @return 修复结果
     */
    @PostMapping("/syncCollectMeterWithActualElectricity")
    public AjaxResult syncCollectMeterWithActualElectricity(@RequestBody SyncCollectMeterWithActualRequest request) {
        try {
            // 参数验证
            String validationError = request.validate();
            if (validationError != null) {
                return AjaxResult.error(validationError);
            }

            log.info("开始修复超出报账的电量: {}", request);

            String result = mssInterfaceServiceImpl.syncCollectMeterWithActualElectricity(
                    request.getPeriodNumber(),
                    request.getWriteoffInstanceCode(),
                    request.getStationCode(),
                    request.getBusinessElectricity()
            );

            log.info("修复超出报账电量结果: {}", result);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("修复超出报账电量异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步采集接口 定时调用存储过程向 power_station_avedayelec 插入数据
     *
     * @param max 最大月份
     * @param min 最小月份
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/callpowerStationAvedayelec")
    public AjaxResult CallpowerStationAvedayelec(@RequestParam(required = false, value = "maxMonth", defaultValue = "12") Integer max, @RequestParam(required = false, value = "minMonth", defaultValue = "0") Integer min) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.CallpowerStationAvedayelec(max, min));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //  集团 同步电表基础数据
    @GetMapping("/syncEnergyMeterInfos/{id}")
    @ResponseBody
    public AjaxResult syncEnergyMeterInfos(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncEnergyMeterInfos(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //  集团 同步电表基础数据 根据报账单id查询 用Electrotype 存放报账单id 进行查询
    @GetMapping("/syncEnergyMeterInfosBybill/{id}")
    @ResponseBody
    public AjaxResult syncEnergyMeterInfosBybill(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncEnergyMeterInfosBybill(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 批量同步电表基础信息
     *
     * @param ids 报账单id列表
     * @return 结果
     * @throws Exception 异常
     */
    @PostMapping("/syncEnergyMeterInfosBybillPro")
    @ResponseBody
    public String syncEnergyMeterInfosBybillPro(@RequestBody List<Long> ids) throws Exception {
        StringJoiner result = new StringJoiner("||", "同步电表执行情况\n", "\n结束");
        ids.forEach(
                billid -> {
                    try {
                        String resJson = mssInterfaceServiceImpl.syncEnergyMeterInfosBybillPro(billid);
                        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
                            result.add(String.format("%d 同步成功", billid));
                        } else {
                            result.add(String.format("%d 同步失败", billid));
                        }

                    } catch (Exception e) {
                        result.add(String.format("%d 同步出现异常", billid));
                    }
                }
        );
        return result.toString();
    }

    /**
     * 批量同步电表用电数据
     *
     * @param ids 报账单id列表
     * @return 结果
     * @throws Exception 异常
     */
    @PostMapping("/syncWriteoffInfosPro")
    @ResponseBody
    public String syncWriteoffInfosPro(@RequestBody List<Long> ids) throws Exception {
        StringJoiner result = new StringJoiner("||", "同步电表用电数据执行情况\n", "\n结束");
        ids.forEach(
                billid -> {
                    try {
                        String resJson = mssInterfaceServiceImpl.selectWriteoffDetailInfoPro(billid, "2");
                        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
                            result.add(String.format("%d 同步成功", billid));
                        } else {
                            result.add(String.format("%d 同步失败", billid));
                        }

                    } catch (Exception e) {
                        result.add(String.format("%d 同步出现异常", billid));
                    }
                }
        );
        return result.toString();
    }

    @GetMapping("/testRecord")
    public AjaxResult testRecord() {
        try {
            mssInterfaceServiceImpl.selectmeterEquipmentInfors("4400249442708643840" + "/create");
        } catch (Exception e) {
            log.error("操作异常", e);
        }
        return AjaxResult.success("ok");
    }


    //  同步集团计量设备数据 根据报账单id查询 用Electrotype 存放报账单id 进行查询
    @GetMapping("/syncmeterEquipmentInfors/{id}")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInforsByBillId(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncEnergyMeterInfosBybillJiLiang(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 双碳电表数据全量生成
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncmeterEquipmentInForTwoC/{id}")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInForTwoC(@PathVariable("id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncmeterEquipmentInForTwoC(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }


    //  同步集团计量设备全量数据
    @GetMapping("/syncmeterEquipmentInfors/all/{id}")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInforsAll(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {

            CompletableFuture.supplyAsync(() -> {
                String message = null;
                try {
                    message = mssInterfaceServiceImpl.syncEnergyMeterInfosAll(id);
                } catch (Exception e) {
                    log.error("操作异常", e);
                }
                return message;
            });
            return this.success("同步计量设备异步任务开始执行");
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    //  同步双碳电表数据全量
    @GetMapping("/syncmeterEquipmentInforTwoc/all/{id}")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInforTwocAll(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncmeterEquipmentInforTwocAll(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步双碳电表实际用电全量数据
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncMeterDateInforTwoc/all/{id}")
    @ResponseBody
    public AjaxResult syncMeterDateInforTwocAll(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncMeterDateInforTwocAll(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步双碳其它能耗数据
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncMeterOtherDateInforTwoc/all/{id}")
    @ResponseBody
    public AjaxResult syncMeterOtherDateInforTwocAll(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncMeterOtherDateInforTwocAll(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步双碳废水废气数据
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncMeterPollutionDateInforTwoc/all/{id}")
    @ResponseBody
    public AjaxResult syncMeterPollutionDateInforTwocAll(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncMeterPollutionDateInforTwoc(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步双碳机房用能同步数据
     *
     * @param premonth 前一个月
     * @param delflag 删除标志
     * @param environment 环境
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncRoomEnergyUse")
    @ResponseBody
    public AjaxResult syncRoomEnergyUse(@RequestParam("premonth") String premonth, @RequestParam("delflag") boolean delflag, @RequestParam("environment") String environment) throws Exception {
        try {
            return AjaxResult.success(mssInterfaceServiceImpl.syncRoomEnergyUse(premonth, delflag, environment));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }

    /**
     * 同步双碳局站用能同步数据
     *
     * @param premonth 前一个月
     * @param delflag 删除标志
     * @param environment 环境
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncStationEnergyUse")
    @ResponseBody
    public AjaxResult syncStationEnergyUse(@RequestParam("premonth") String premonth, @RequestParam("delflag") boolean delflag, @RequestParam("environment") String environment) throws Exception {
        try {
            return AjaxResult.success(mssInterfaceServiceImpl.syncStationEnergyUse(premonth, delflag, environment));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }


    /**
     * 辽宁测试地址
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncMeterPollutionDateInforTwoc/test1")
    @ResponseBody
    public AjaxResult testForLn1(@RequestParam("id") String id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.SyncTest(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }


    //同步集团全量设备 更正
    /**
     * 同步集团全量设备 更正
     *
     * @param id 报账单id
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/syncmeterEquipmentInfors/all/corrections")
    @ResponseBody
    public AjaxResult syncmeterEquipmentInforsAllCorrections(@PathVariable(required = false, value = "id") Long id) throws Exception {
        try {
            return this.success(mssInterfaceServiceImpl.syncEnergyMeterInfosAll(id));
        } catch (Exception e) {
            log.error("操作异常", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return this.error(sw.toString());
        }
    }


    /**
     * 根据id获取组织
     *
     * @param id 组织id
     * @return 结果
     */
    @GetMapping("/getOrg/{id}")
    @ResponseBody
    public AjaxResult getOrg(@PathVariable("id") String id) {
        List<ViewOrgSapCostCenter> list;
        try {
            list = mssInterfaceService.getFileorgCode(id);
        } catch (Exception e) {
            return this.error("系统异常，请联系管理员" + e.getMessage());
        }
        Object object = JSONObject.toJSON(list);
        return this.success(object);
    }

    //根据 财辅报账单 instanceCode 获取 sap凭证
    /**
     * 根据id获取sap凭证
     *
     * @param id 报账单id
     * @return 结果
     */
    @GetMapping("/getSap/{id}")
    @ResponseBody
    public AjaxResult getSap(@PathVariable("id") String id) {
        Object object;
        try {
            Map<String, Object> map = mssInterfaceService.getSapMesByInstanceCode(id);
            object = JSONObject.toJSON(map);
        } catch (Exception e) {
            return this.error("未查询到SAP凭证，系统异常，请联系管理员" + e.getMessage());
        }
        return this.success(object);
    }

    /**
     * 手动推送报账单 xml
     *
     * @param xml 报账单xml
     * @return 结果
     * @throws Exception 异常
     */
    @PostMapping("/sendToMssXml")
    @ResponseBody
    public AjaxResult sendToMssXml(String xml) throws Exception {
        AjaxResult rs = new AjaxResult();
        String s = mssInterfaceServiceImpl.doMssHttp(xml);
        rs.put("res", s);
        return rs;
    }

    /**
     * 新增4G报账
     */
    @GetMapping("/add4g/{id}")
    @ResponseBody
    public AjaxResult add4g(@PathVariable("id") Long id) {
        MssAccountbill mssAccountbill = mssAccountbillService.getByid(id);
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            mssAccountbillService.insertjtltem(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", "保存失败,请联系系统管理员");
            log.error("操作异常", e);
        }
        json.put("data", map);
        return json;
    }

    /**
     * 轮询任务
     */
    //@Log(title = "MSS财务接口 轮询任务 10分钟一次", action = BusinessType.INSERT)
    @GetMapping("/sendToMssByError")
    public void sendToMssByError() {
//        放前面
        mssInterfaceService.sendToMssByError();// 送财辅失败的 重新送一次
        //mssInterfaceService.getStausBybills(); // 查询报账单状态
    }


    /**
     * 更改报账单轮询分支
     *
     * @param doSyncSpanInfoFalg3 是否启用异步多线程轮询
     * @return 结果
     * @throws Exception 异常
     */
    @GetMapping("/updatedoSyncSpanInfoFalg3")
    @ResponseBody
    public String updatedoSyncSpanInfoFalg3(@RequestParam(value = "doSyncSpanInfoFalg3") boolean doSyncSpanInfoFalg3) throws Exception {
        MssInterfaceController.doSyncSpanInfoFalg3 = doSyncSpanInfoFalg3;
        boolean falg = MssInterfaceController.doSyncSpanInfoFalg3;
        return String.format("当前轮询报账单status=3为:%s", falg ? "传统单线程轮询" : "异步多线程轮询");
    }

    /**
     * 检查账单报账站点是否在集团能源平台清单中
     *
     * @param billId 报账单id
     * @return 结果
     */
    @GetMapping("/checkBillStations")
    @ResponseBody
    public AjaxResult checkBillStations(String billId) {
        String res = mssInterfaceServiceImpl.checkBillStations(Long.parseLong(billId));
        return AjaxResult.success(res);
    }
}
